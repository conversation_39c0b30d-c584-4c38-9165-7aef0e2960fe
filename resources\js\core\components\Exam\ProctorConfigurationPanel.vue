<template>
    <div class="space-y-6">
        <!-- Main Proctoring Toggle -->
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <BaseSwitch
                vertical
                v-model="localConfig.enableProctoring"
                name="enableProctoring"
                :label="$trans('exam.proctoring.config.enable_proctoring') || 'Enable Proctoring'"
                :description="$trans('exam.proctoring.config.enable_proctoring_help') || 'Enable comprehensive monitoring and security features for this exam'"
                @change="onEnableProctoringChange"
            />
        </div>

        <!-- Proctoring Configuration Options -->
        <div v-if="localConfig.enableProctoring" class="space-y-6">
            <!-- Quick Setup Presets -->
            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                <ProctorPresetSelector
                    @preset-selected="onPresetSelected"
                />
            </div>
            <!-- Monitoring Features -->
            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    {{ $trans("exam.proctoring.config.monitoring_features") || "Monitoring Features" }}
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Webcam Monitoring -->
                    <div class="space-y-3">
                        <BaseSwitch
                            v-model="localConfig.proctorConfig.webcamMonitoring"
                            name="webcamMonitoring"
                            :label="$trans('exam.proctoring.config.webcam_monitoring') || 'Webcam Monitoring'"
                            :description="$trans('exam.proctoring.config.webcam_monitoring_help') || 'Capture periodic images from student webcam'"
                        />

                        <div v-if="localConfig.proctorConfig.webcamMonitoring" class="ml-6 space-y-3">
                            <BaseInput
                                type="number"
                                v-model="localConfig.proctorConfig.captureIntervalSeconds"
                                name="captureInterval"
                                :label="$trans('exam.proctoring.config.capture_interval') || 'Capture Interval (seconds)'"
                                min="10"
                                max="300"
                                class="max-w-xs"
                            />
                            <HelperText>{{ $trans("exam.proctoring.config.capture_interval_help") || "How often to capture webcam images (10-300 seconds)" }}</HelperText>
                        </div>
                    </div>

                    <!-- Microphone Monitoring -->
                    <div class="space-y-3">
                        <BaseSwitch
                            v-model="localConfig.proctorConfig.microphoneMonitoring"
                            name="microphoneMonitoring"
                            :label="$trans('exam.proctoring.config.microphone_monitoring') || 'Microphone Monitoring'"
                            :description="$trans('exam.proctoring.config.microphone_monitoring_help') || 'Monitor audio levels and detect external voices'"
                        />

                        <div v-if="localConfig.proctorConfig.microphoneMonitoring" class="ml-6 space-y-3">
                            <BaseInput
                                type="number"
                                v-model="localConfig.proctorConfig.audioThresholdDb"
                                name="audioThreshold"
                                :label="$trans('exam.proctoring.config.audio_threshold') || 'Audio Threshold (dB)'"
                                min="-80"
                                max="0"
                                class="max-w-xs"
                            />
                            <HelperText>{{ $trans("exam.proctoring.config.audio_threshold_help") || "Audio level threshold for alerts (-80 to 0 dB)" }}</HelperText>
                        </div>
                    </div>

                    <!-- Face Detection -->
                    <div class="space-y-3">
                        <BaseSwitch
                            v-model="localConfig.proctorConfig.faceDetection"
                            name="faceDetection"
                            :label="$trans('exam.proctoring.config.face_detection') || 'Face Detection'"
                            :description="$trans('exam.proctoring.config.face_detection_help') || 'AI-powered face detection and identity verification'"
                        />

                        <div v-if="localConfig.proctorConfig.faceDetection" class="ml-6 space-y-3">
                            <BaseInput
                                type="number"
                                v-model="localConfig.proctorConfig.maxFaceDetectionFailures"
                                name="maxFaceFailures"
                                :label="$trans('exam.proctoring.config.max_face_failures') || 'Max Face Detection Failures'"
                                min="1"
                                max="20"
                                class="max-w-xs"
                            />
                            <HelperText>{{ $trans("exam.proctoring.config.max_face_failures_help") || "Maximum consecutive face detection failures before alert" }}</HelperText>
                        </div>
                    </div>

                    <!-- Screen Recording -->
                    <div class="space-y-3">
                        <BaseSwitch
                            v-model="localConfig.proctorConfig.screenRecording"
                            name="screenRecording"
                            :label="$trans('exam.proctoring.config.screen_recording') || 'Screen Recording'"
                            :description="$trans('exam.proctoring.config.screen_recording_help') || 'Record screen activity and detect tab switching'"
                        />
                    </div>
                </div>
            </div>

            <!-- Security Features -->
            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    {{ $trans("exam.proctoring.config.security_features") || "Security Features" }}
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Fullscreen Enforcement -->
                    <div class="space-y-3">
                        <BaseSwitch
                            v-model="localConfig.proctorConfig.fullscreenEnforcement"
                            name="fullscreenEnforcement"
                            :label="$trans('exam.proctoring.config.fullscreen_enforcement') || 'Fullscreen Enforcement'"
                            :description="$trans('exam.proctoring.config.fullscreen_enforcement_help') || 'Force exam to run in fullscreen mode'"
                        />
                    </div>

                    <!-- Copy-Paste Blocking -->
                    <div class="space-y-3">
                        <BaseSwitch
                            v-model="localConfig.proctorConfig.copyPasteBlocking"
                            name="copyPasteBlocking"
                            :label="$trans('exam.proctoring.config.copy_paste_blocking') || 'Copy-Paste Blocking'"
                            :description="$trans('exam.proctoring.config.copy_paste_blocking_help') || 'Block copy, cut, and paste operations'"
                        />
                    </div>

                    <!-- Tab Switching -->
                    <div class="space-y-3">
                        <BaseSwitch
                            v-model="localConfig.proctorConfig.allowTabSwitching"
                            name="allowTabSwitching"
                            :label="$trans('exam.proctoring.config.allow_tab_switching') || 'Allow Tab Switching'"
                            :description="$trans('exam.proctoring.config.allow_tab_switching_help') || 'Allow students to switch between browser tabs'"
                        />
                    </div>

                    <!-- Auto Submit on Violations -->
                    <div class="space-y-3">
                        <BaseSwitch
                            v-model="localConfig.proctorConfig.autoSubmitOnViolations"
                            name="autoSubmitOnViolations"
                            :label="$trans('exam.proctoring.config.auto_submit_on_violations') || 'Auto Submit on Violations'"
                            :description="$trans('exam.proctoring.config.auto_submit_on_violations_help') || 'Automatically submit exam when critical violations are detected'"
                        />
                    </div>
                </div>
            </div>

            <!-- Advanced Settings -->
            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    {{ $trans("exam.proctoring.config.advanced_settings") || "Advanced Settings" }}
                </h3>
                
                <div class="space-y-4">
                    <!-- Proctoring Strictness Level -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {{ $trans("exam.proctoring.config.strictness_level") || "Proctoring Strictness Level" }}
                        </label>
                        <BaseSelect
                            v-model="strictnessLevel"
                            name="strictnessLevel"
                            :options="strictnessOptions"
                            @change="applyStrictnessLevel"
                            class="max-w-xs"
                        />
                        <HelperText>{{ $trans("exam.proctoring.config.strictness_level_help") || "Pre-configured security levels for different exam types" }}</HelperText>
                    </div>

                    <!-- Custom Instructions -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {{ $trans("exam.proctoring.config.custom_instructions") || "Custom Proctoring Instructions" }}
                        </label>
                        <BaseTextarea
                            v-model="localConfig.proctorConfig.customInstructions"
                            name="customInstructions"
                            :placeholder="$trans('exam.proctoring.config.custom_instructions_placeholder') || 'Additional instructions for students regarding proctoring requirements...'"
                            :rows="3"
                        />
                        <HelperText>{{ $trans("exam.proctoring.config.custom_instructions_help") || "Additional instructions shown to students before exam starts" }}</HelperText>
                    </div>
                </div>
            </div>

            <!-- Preview Section -->
            <div class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    {{ $trans("exam.proctoring.config.configuration_preview") || "Configuration Preview" }}
                </h3>
                
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 rounded-full" :class="localConfig.proctorConfig.webcamMonitoring ? 'bg-green-500' : 'bg-gray-300'"></div>
                        <span>{{ $trans("exam.proctoring.config.webcam") || "Webcam" }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 rounded-full" :class="localConfig.proctorConfig.microphoneMonitoring ? 'bg-green-500' : 'bg-gray-300'"></div>
                        <span>{{ $trans("exam.proctoring.config.microphone") || "Microphone" }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 rounded-full" :class="localConfig.proctorConfig.faceDetection ? 'bg-green-500' : 'bg-gray-300'"></div>
                        <span>{{ $trans("exam.proctoring.config.face_detection") || "Face Detection" }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 rounded-full" :class="localConfig.proctorConfig.fullscreenEnforcement ? 'bg-green-500' : 'bg-gray-300'"></div>
                        <span>{{ $trans("exam.proctoring.config.fullscreen") || "Fullscreen" }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 rounded-full" :class="localConfig.proctorConfig.copyPasteBlocking ? 'bg-green-500' : 'bg-gray-300'"></div>
                        <span>{{ $trans("exam.proctoring.config.copy_paste") || "Copy/Paste Block" }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 rounded-full" :class="localConfig.proctorConfig.screenRecording ? 'bg-green-500' : 'bg-gray-300'"></div>
                        <span>{{ $trans("exam.proctoring.config.screen_recording") || "Screen Recording" }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 rounded-full" :class="!localConfig.proctorConfig.allowTabSwitching ? 'bg-green-500' : 'bg-gray-300'"></div>
                        <span>{{ $trans("exam.proctoring.config.tab_blocking") || "Tab Blocking" }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 rounded-full" :class="localConfig.proctorConfig.autoSubmitOnViolations ? 'bg-yellow-500' : 'bg-gray-300'"></div>
                        <span>{{ $trans("exam.proctoring.config.auto_submit") || "Auto Submit" }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "ProctorConfigurationPanel",
}
</script>

<script setup>
import { ref, reactive, watch } from 'vue'
import ProctorPresetSelector from './ProctorPresetSelector.vue'

const props = defineProps({
    modelValue: {
        type: Object,
        default: () => ({
            enableProctoring: false,
            proctorConfig: {}
        })
    },
    error: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['update:modelValue'])

// Default proctoring configuration - matches backend field names (camelCase after axios transformation)
const defaultProctorConfig = {
    webcamMonitoring: true,
    microphoneMonitoring: true,
    screenRecording: true,
    fullscreenEnforcement: true,
    copyPasteBlocking: true,
    faceDetection: true,
    captureIntervalSeconds: 30,
    audioThresholdDb: -40,
    maxFaceDetectionFailures: 5,
    allowTabSwitching: false,
    autoSubmitOnViolations: false,
    customInstructions: ''
}

// Local configuration state
const localConfig = reactive({
    enableProctoring: props.modelValue.enableProctoring || false,
    proctorConfig: { ...defaultProctorConfig, ...props.modelValue.proctorConfig }
})

// Strictness level for quick configuration
const strictnessLevel = ref('medium')
const strictnessOptions = [
    { label: 'Low Security', value: 'low' },
    { label: 'Medium Security', value: 'medium' },
    { label: 'High Security', value: 'high' },
    { label: 'Maximum Security', value: 'maximum' },
    { label: 'Custom', value: 'custom' }
]

// Watch for prop changes and update local state
watch(() => props.modelValue, (newValue) => {
    localConfig.enableProctoring = newValue.enableProctoring || false
    // Use saved configuration as-is, only fill missing fields with defaults
    // This preserves the exact saved state including disabled features
    const savedConfig = newValue.proctorConfig || {}
    localConfig.proctorConfig = {}

    // Set each field from saved config or default
    Object.keys(defaultProctorConfig).forEach(key => {
        localConfig.proctorConfig[key] = savedConfig.hasOwnProperty(key)
            ? savedConfig[key]
            : defaultProctorConfig[key]
    })
}, { deep: true, immediate: true })

// Watch for changes and emit to parent
watch(localConfig, (newValue) => {
    emit('update:modelValue', {
        enableProctoring: newValue.enableProctoring,
        proctorConfig: newValue.enableProctoring ? newValue.proctorConfig : {}
    })
}, { deep: true })

// Handle enabling/disabling proctoring
const onEnableProctoringChange = (enabled) => {
    if (enabled) {
        // Reset to default configuration when enabling
        Object.assign(localConfig.proctorConfig, defaultProctorConfig)
    }
}

// Handle preset selection
const onPresetSelected = (preset) => {
    if (preset.config.enableProctoring !== undefined) {
        localConfig.enableProctoring = preset.config.enableProctoring
    }

    if (preset.config.enableProctoring) {
        // Extract proctoring config (exclude enableProctoring)
        const { enableProctoring, ...proctorConfig } = preset.config
        Object.assign(localConfig.proctorConfig, proctorConfig)
    }
}

// Apply strictness level presets
const applyStrictnessLevel = (level) => {
    const presets = {
        low: {
            webcamMonitoring: true,
            microphoneMonitoring: false,
            screenRecording: false,
            fullscreenEnforcement: false,
            copyPasteBlocking: true,
            faceDetection: false,
            captureIntervalSeconds: 60,
            audioThresholdDb: -30,
            maxFaceDetectionFailures: 10,
            allowTabSwitching: true,
            autoSubmitOnViolations: false
        },
        medium: {
            webcamMonitoring: true,
            microphoneMonitoring: true,
            screenRecording: true,
            fullscreenEnforcement: true,
            copyPasteBlocking: true,
            faceDetection: true,
            captureIntervalSeconds: 30,
            audioThresholdDb: -40,
            maxFaceDetectionFailures: 5,
            allowTabSwitching: false,
            autoSubmitOnViolations: false
        },
        high: {
            webcamMonitoring: true,
            microphoneMonitoring: true,
            screenRecording: true,
            fullscreenEnforcement: true,
            copyPasteBlocking: true,
            faceDetection: true,
            captureIntervalSeconds: 20,
            audioThresholdDb: -50,
            maxFaceDetectionFailures: 3,
            allowTabSwitching: false,
            autoSubmitOnViolations: true
        },
        maximum: {
            webcamMonitoring: true,
            microphoneMonitoring: true,
            screenRecording: true,
            fullscreenEnforcement: true,
            copyPasteBlocking: true,
            faceDetection: true,
            captureIntervalSeconds: 10,
            audioThresholdDb: -60,
            maxFaceDetectionFailures: 1,
            allowTabSwitching: false,
            autoSubmitOnViolations: true
        }
    }

    if (presets[level]) {
        Object.assign(localConfig.proctorConfig, presets[level])
    }
}
</script>
