<template>
    <BaseModal :show="visibility" @close="$emit('close')" size="lg">
        <template #title>
            <div class="flex items-center space-x-2">
                <component :is="getEventIcon(event)" class="w-5 h-5" :class="getIconColor(event?.severity)" />
                <span>{{ getEventTitle(event?.eventType) }} {{ console.log("Look here ", getEventIcon(event).template) }}</span>
                <span
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    :class="getSeverityClass(event?.severity)"
                >
                    {{ event?.severity || 'unknown' }}
                </span>
            </div>
        </template>

        <div class="space-y-6">
            <!-- Basic Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <BaseDataView label="Event Type">
                    {{ getEventTitle(event?.eventType) }}
                </BaseDataView>
                <BaseDataView label="Severity">
                    <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        :class="getSeverityClass(event?.severity)"
                    >
                        {{ event?.severity || 'unknown' }}
                    </span>
                </BaseDataView>
                <BaseDataView label="Detected At">
                    {{ formatDateTime(event?.detectedAt?.formatted) }}
                </BaseDataView>
                <BaseDataView label="IP Address">
                    {{ event?.ipAddress || 'N/A' }}
                </BaseDataView>
            </div>

            <!-- Description -->
            <div v-if="event?.description">
                <BaseDataView label="Description">
                    {{ event?.formattedDescription || event?.description }}
                </BaseDataView>
            </div>

            <!-- Event Data -->
            <div v-if="event?.data && Object.keys(event.data).length > 0" class="space-y-4">
                <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                    {{ $trans("exam.proctoring.review.event_data") || "Event Data" }}
                </h4>
                
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <!-- Face Detection Data -->
                    <div v-if="event?.data?.faceCount !== undefined" class="space-y-2">
                        <div class="flex justify-between">
                            <span class="font-medium">Faces Detected:</span>
                            <span>{{ event.data.faceCount }}</span>
                        </div>
                        <div v-if="event?.data?.faces && event.data.faces.length > 0" class="space-y-2">
                            <h5 class="font-medium">Face Details:</h5>
                            <div v-for="(face, index) in event.data.faces" :key="index" class="text-sm">
                                <div class="flex justify-between">
                                    <span>Face {{ index + 1 }} Confidence:</span>
                                    <span>{{ Math.round((face?.confidence || 0) * 100) }}%</span>
                                </div>
                                <div v-if="face?.box" class="text-xs text-gray-600 dark:text-gray-400">
                                    Position: {{ Math.round(face.box.x) }}, {{ Math.round(face.box.y) }}
                                    ({{ Math.round(face.box.width) }}×{{ Math.round(face.box.height) }})
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Audio Data -->
                    <div v-if="event?.data?.audioLevel !== undefined" class="space-y-2">
                        <div class="flex justify-between">
                            <span class="font-medium">Audio Level:</span>
                            <span>{{ event.data.audioLevel }}dB</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div
                                class="h-2 rounded-full"
                                :class="getAudioLevelColor(event.data.audioLevel)"
                                :style="{ width: getAudioLevelPercentage(event.data.audioLevel) + '%' }"
                            ></div>
                        </div>
                    </div>

                    <!-- Tab Switch Data -->
                    <div v-if="event?.data?.tabTitle" class="space-y-2">
                        <div class="flex justify-between">
                            <span class="font-medium">Tab Title:</span>
                            <span class="text-right">{{ event.data.tabTitle }}</span>
                        </div>
                    </div>

                    <!-- Copy/Paste Data -->
                    <div v-if="event?.data?.action" class="space-y-2">
                        <div class="flex justify-between">
                            <span class="font-medium">Action:</span>
                            <span>{{ event.data.action }}</span>
                        </div>
                        <div v-if="event?.data?.content" class="space-y-2">
                            <span class="font-medium">Content:</span>
                            <div class="bg-white dark:bg-gray-800 p-2 rounded border text-sm">
                                {{ event.data.content }}
                            </div>
                        </div>
                    </div>

                    <!-- Generic Data Display -->
                    <div v-for="(value, key) in filteredEventData" :key="key" class="flex justify-between">
                        <span class="font-medium">{{ formatKey(key) }}:</span>
                        <span>{{ formatValue(value) }}</span>
                    </div>
                </div>
            </div>

            <!-- Media Preview -->
            <div v-if="event?.hasMedia && event?.mediaUrl" class="space-y-4">
                <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                    {{ $trans("exam.proctoring.review.media") || "Media" }}
                </h4>

                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div v-if="isImageEvent(event?.eventType)" class="text-center">
                        <img
                            :src="event.mediaUrl"
                            :alt="getEventTitle(event?.eventType)"
                            class="max-w-full h-auto rounded-lg shadow-md mx-auto"
                            style="max-height: 300px;"
                        />
                        <div class="mt-2">
                            <BaseButton
                                size="sm"
                                design="secondary"
                                @click="openMediaInNewTab"
                            >
                                <i class="fas fa-external-link-alt mr-1"></i>
                                {{ $trans("general.open_in_new_tab") || "Open in New Tab" }}
                            </BaseButton>
                        </div>
                    </div>

                    <div v-else-if="isAudioEvent(event?.eventType)" class="text-center">
                        <audio controls class="w-full">
                            <source :src="event.mediaUrl" type="audio/wav">
                            <source :src="event.mediaUrl" type="audio/mp3">
                            Your browser does not support the audio element.
                        </audio>
                        <div class="mt-2">
                            <BaseButton
                                size="sm"
                                design="secondary"
                                @click="openMediaInNewTab"
                            >
                                <i class="fas fa-download mr-1"></i>
                                {{ $trans("general.download") || "Download" }}
                            </BaseButton>
                        </div>
                    </div>

                    <div v-else class="text-center">
                        <BaseButton
                            design="primary"
                            @click="openMediaInNewTab"
                        >
                            <i class="fas fa-external-link-alt mr-1"></i>
                            {{ $trans("exam.proctoring.review.view_media") || "View Media" }}
                        </BaseButton>
                    </div>
                </div>
            </div>

            <!-- Technical Details -->
            <div class="space-y-4">
                <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                    {{ $trans("exam.proctoring.review.technical_details") || "Technical Details" }}
                </h4>
                
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="font-medium">Event UUID:</span>
                        <span class="font-mono">{{ event?.uuid || 'N/A' }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-medium">User Agent:</span>
                        <span class="text-right max-w-md truncate">{{ event?.userAgent || 'N/A' }}</span>
                    </div>
                    <div v-if="event?.meta && Object.keys(event.meta).length > 0" class="space-y-1">
                        <span class="font-medium">Metadata:</span>
                        <pre class="text-xs bg-white dark:bg-gray-800 p-2 rounded overflow-auto">{{ JSON.stringify(event.meta, null, 2) }}</pre>
                    </div>
                </div>
            </div>
        </div>

        <div class="flex justify-end space-x-2 p-4">
            <BaseButton design="secondary" @click="$emit('close')">
                {{ $trans("general.close") || "Close" }}
            </BaseButton>
            <BaseButton v-if="event?.hasMedia" design="primary" @click="openMediaInNewTab">
                <i class="fas fa-external-link-alt mr-1"></i>
                {{ $trans("exam.proctoring.review.view_media") || "View Media" }}
            </BaseButton>
        </div>
    </BaseModal>
</template>

<script>
export default {
    name: "ProctorEventDetailModal",
}
</script>

<script setup>
import { computed } from 'vue'

defineEmits(['close'])

const props = defineProps({
    event: {
        type: Object,
        required: true
    },
    visibility: {
        type: Boolean,
        default: false
    }
})

// Same helper functions as ProctorEventTimeline
const getEventIcon = (event) => {
    const iconMap = {
        webcam_capture: {
            template: `<svg fill="currentColor" viewBox="0 0 20 20"><path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"></path></svg>`
        },
        audio_alert: {
            template: `<svg fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clip-rule="evenodd"></path></svg>`
        },
        tab_switch: {
            template: `<svg fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12z" clip-rule="evenodd"></path></svg>`
        },
        fullscreen_exit: {
            template: `<svg fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4z" clip-rule="evenodd"></path></svg>`
        },
        copy_paste_attempt: {
            template: `<svg fill="currentColor" viewBox="0 0 20 20"><path d="M8 2a1 1 0 000 2h2a1 1 0 100-2H8z"></path><path d="M3 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6h-4.586l1.293-1.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L10.414 13H15v3a2 2 0 01-2 2H5a2 2 0 01-2-2V5z"></path></svg>`
        },
        face_detection_failure: {
            template: `<svg fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"></path></svg>`
        },
        suspicious_activity: {
            template: `<svg fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>`
        },
        proctoring_started: {
            template: `<svg fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm.75-12a.75.75 0 00-1.5 0v4c0 .414.336.75.75.75h3a.75.75 0 000-1.5H10.75V6z" clip-rule="evenodd"/></svg>`
        },
        proctoring_ended: {
            template: `<svg fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10 2a1 1 0 011 1v1.268a7.5 7.5 0 11-2 0V3a1 1 0 011-1zm0 4a6 6 0 100 12 6 6 0 000-12zm.75 3a.75.75 0 00-1.5 0v3.5c0 .414.336.75.75.75s.75-.336.75-.75V9z"/></svg>`
        }
    }

    // Check if event and event_type exist
    if (!event || !event.eventType) {
        return iconMap.suspicious_activity
    }

    return iconMap[event.eventType] || iconMap.suspicious_activity
}

const getEventTitle = (eventType) => {
    const titleMap = {
        webcam_capture: 'Webcam Capture',
        audio_alert: 'Audio Alert',
        tab_switch: 'Tab Switch Detected',
        fullscreen_exit: 'Fullscreen Exit',
        copy_paste_attempt: 'Copy/Paste Attempt',
        face_detection_failure: 'Face Detection Failure',
        suspicious_activity: 'Suspicious Activity',
        proctoring_started: 'Proctoring Started',
        proctoring_ended: 'Proctoring Ended'
    }

    // Check if eventType exists
    if (!eventType) {
        return 'Unknown Event'
    }

    return titleMap[eventType] || eventType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const getSeverityClass = (severity) => {
    if (!severity) {
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }

    switch (severity) {
        case 'critical':
            return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
        case 'warning':
            return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
        case 'info':
            return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
        default:
            return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
}

const getIconColor = (severity) => {
    if (!severity) {
        return 'text-gray-500'
    }

    switch (severity) {
        case 'critical':
            return 'text-red-500'
        case 'warning':
            return 'text-yellow-500'
        case 'info':
            return 'text-blue-500'
        default:
            return 'text-gray-500'
    }
}

const filteredEventData = computed(() => {
    if (!props.event?.data) return {}

    // Filter out data that's already displayed in specific sections
    const excludeKeys = ['face_count', 'faces', 'audio_level', 'tab_title', 'action', 'content']

    return Object.fromEntries(
        Object.entries(props.event.data).filter(([key]) => !excludeKeys.includes(key))
    )
})

const formatDateTime = (timestamp) => {
    if (!timestamp) return 'N/A'
    
    const date = new Date(timestamp)
    return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    })
}

const formatKey = (key) => {
    return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const formatValue = (value) => {
    if (typeof value === 'object') {
        return JSON.stringify(value)
    }
    return String(value)
}

const isImageEvent = (eventType) => {
    return ['webcam_capture', 'screen_capture'].includes(eventType)
}

const isAudioEvent = (eventType) => {
    return ['audio_alert', 'audio_recording'].includes(eventType)
}

const getAudioLevelColor = (level) => {
    if (level > -20) return 'bg-red-500'
    if (level > -40) return 'bg-yellow-500'
    return 'bg-green-500'
}

const getAudioLevelPercentage = (level) => {
    // Convert dB level to percentage (assuming range from -80dB to 0dB)
    const minDb = -80
    const maxDb = 0
    const percentage = ((level - minDb) / (maxDb - minDb)) * 100
    return Math.max(0, Math.min(100, percentage))
}

const openMediaInNewTab = () => {
    if (props.event?.mediaUrl) {
        window.open(props.event.mediaUrl, '_blank')
    }
}
</script>
