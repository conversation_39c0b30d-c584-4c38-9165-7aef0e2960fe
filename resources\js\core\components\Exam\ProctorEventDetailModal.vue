<template>
    <BaseModal :show="visibility" @close="$emit('close')" size="lg">
        <template #title>
            <div class="flex items-center space-x-2">
                <component :is="getEventIcon(event)" class="w-5 h-5" :class="getIconColor(event.severity)" />
                <span>{{ getEventTitle(event.event_type) }}</span>
                <span
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    :class="getSeverityClass(event.severity)"
                >
                    {{ event.severity }}
                </span>
            </div>
        </template>

        <div class="space-y-6">
            <!-- Basic Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <BaseDataView label="Event Type">
                    {{ getEventTitle(event.event_type) }}
                </BaseDataView>
                <BaseDataView label="Severity">
                    <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        :class="getSeverityClass(event.severity)"
                    >
                        {{ event.severity }}
                    </span>
                </BaseDataView>
                <BaseDataView label="Detected At">
                    {{ formatDateTime(event.detected_at) }}
                </BaseDataView>
                <BaseDataView label="IP Address">
                    {{ event.ip_address || 'N/A' }}
                </BaseDataView>
            </div>

            <!-- Description -->
            <div v-if="event.description">
                <BaseDataView label="Description">
                    {{ event.formatted_description || event.description }}
                </BaseDataView>
            </div>

            <!-- Event Data -->
            <div v-if="event.data && Object.keys(event.data).length > 0" class="space-y-4">
                <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                    {{ $trans("exam.proctoring.review.event_data") || "Event Data" }}
                </h4>
                
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <!-- Face Detection Data -->
                    <div v-if="event.data.face_count !== undefined" class="space-y-2">
                        <div class="flex justify-between">
                            <span class="font-medium">Faces Detected:</span>
                            <span>{{ event.data.face_count }}</span>
                        </div>
                        <div v-if="event.data.faces && event.data.faces.length > 0" class="space-y-2">
                            <h5 class="font-medium">Face Details:</h5>
                            <div v-for="(face, index) in event.data.faces" :key="index" class="text-sm">
                                <div class="flex justify-between">
                                    <span>Face {{ index + 1 }} Confidence:</span>
                                    <span>{{ Math.round(face.confidence * 100) }}%</span>
                                </div>
                                <div v-if="face.box" class="text-xs text-gray-600 dark:text-gray-400">
                                    Position: {{ Math.round(face.box.x) }}, {{ Math.round(face.box.y) }} 
                                    ({{ Math.round(face.box.width) }}×{{ Math.round(face.box.height) }})
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Audio Data -->
                    <div v-if="event.data.audio_level !== undefined" class="space-y-2">
                        <div class="flex justify-between">
                            <span class="font-medium">Audio Level:</span>
                            <span>{{ event.data.audio_level }}dB</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div 
                                class="h-2 rounded-full"
                                :class="getAudioLevelColor(event.data.audio_level)"
                                :style="{ width: getAudioLevelPercentage(event.data.audio_level) + '%' }"
                            ></div>
                        </div>
                    </div>

                    <!-- Tab Switch Data -->
                    <div v-if="event.data.tab_title" class="space-y-2">
                        <div class="flex justify-between">
                            <span class="font-medium">Tab Title:</span>
                            <span class="text-right">{{ event.data.tab_title }}</span>
                        </div>
                    </div>

                    <!-- Copy/Paste Data -->
                    <div v-if="event.data.action" class="space-y-2">
                        <div class="flex justify-between">
                            <span class="font-medium">Action:</span>
                            <span>{{ event.data.action }}</span>
                        </div>
                        <div v-if="event.data.content" class="space-y-2">
                            <span class="font-medium">Content:</span>
                            <div class="bg-white dark:bg-gray-800 p-2 rounded border text-sm">
                                {{ event.data.content }}
                            </div>
                        </div>
                    </div>

                    <!-- Generic Data Display -->
                    <div v-for="(value, key) in filteredEventData" :key="key" class="flex justify-between">
                        <span class="font-medium">{{ formatKey(key) }}:</span>
                        <span>{{ formatValue(value) }}</span>
                    </div>
                </div>
            </div>

            <!-- Media Preview -->
            <div v-if="event.has_media && event.media_url" class="space-y-4">
                <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                    {{ $trans("exam.proctoring.review.media") || "Media" }}
                </h4>
                
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div v-if="isImageEvent(event.event_type)" class="text-center">
                        <img 
                            :src="event.media_url" 
                            :alt="getEventTitle(event.event_type)"
                            class="max-w-full h-auto rounded-lg shadow-md mx-auto"
                            style="max-height: 300px;"
                        />
                        <div class="mt-2">
                            <BaseButton
                                size="sm"
                                design="secondary"
                                @click="openMediaInNewTab"
                            >
                                <i class="fas fa-external-link-alt mr-1"></i>
                                {{ $trans("general.open_in_new_tab") || "Open in New Tab" }}
                            </BaseButton>
                        </div>
                    </div>
                    
                    <div v-else-if="isAudioEvent(event.event_type)" class="text-center">
                        <audio controls class="w-full">
                            <source :src="event.media_url" type="audio/wav">
                            <source :src="event.media_url" type="audio/mp3">
                            Your browser does not support the audio element.
                        </audio>
                        <div class="mt-2">
                            <BaseButton
                                size="sm"
                                design="secondary"
                                @click="openMediaInNewTab"
                            >
                                <i class="fas fa-download mr-1"></i>
                                {{ $trans("general.download") || "Download" }}
                            </BaseButton>
                        </div>
                    </div>
                    
                    <div v-else class="text-center">
                        <BaseButton
                            design="primary"
                            @click="openMediaInNewTab"
                        >
                            <i class="fas fa-external-link-alt mr-1"></i>
                            {{ $trans("exam.proctoring.review.view_media") || "View Media" }}
                        </BaseButton>
                    </div>
                </div>
            </div>

            <!-- Technical Details -->
            <div class="space-y-4">
                <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                    {{ $trans("exam.proctoring.review.technical_details") || "Technical Details" }}
                </h4>
                
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="font-medium">Event UUID:</span>
                        <span class="font-mono">{{ event.uuid }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-medium">User Agent:</span>
                        <span class="text-right max-w-md truncate">{{ event.user_agent || 'N/A' }}</span>
                    </div>
                    <div v-if="event.meta && Object.keys(event.meta).length > 0" class="space-y-1">
                        <span class="font-medium">Metadata:</span>
                        <pre class="text-xs bg-white dark:bg-gray-800 p-2 rounded overflow-auto">{{ JSON.stringify(event.meta, null, 2) }}</pre>
                    </div>
                </div>
            </div>
        </div>

        <template #footer>
            <div class="flex justify-end space-x-2">
                <BaseButton design="secondary" @click="$emit('close')">
                    {{ $trans("general.close") || "Close" }}
                </BaseButton>
                <BaseButton v-if="event.has_media" design="primary" @click="openMediaInNewTab">
                    <i class="fas fa-external-link-alt mr-1"></i>
                    {{ $trans("exam.proctoring.review.view_media") || "View Media" }}
                </BaseButton>
            </div>
        </template>
    </BaseModal>
</template>

<script>
export default {
    name: "ProctorEventDetailModal",
}
</script>

<script setup>
import { computed } from 'vue'

defineEmits(['close'])

const props = defineProps({
    event: {
        type: Object,
        required: true
    },
    visibility: {
        type: Boolean,
        default: false
    }
})

// Same helper functions as ProctorEventTimeline
const getEventIcon = (event) => {
    // Implementation same as ProctorEventTimeline
    const iconMap = {
        webcam_capture: () => ({
            template: `<svg fill="currentColor" viewBox="0 0 20 20"><path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"></path></svg>`
        }),
        // ... other icons
    }
    
    return iconMap[event.event_type] || iconMap.suspicious_activity
}

const getEventTitle = (eventType) => {
    const titleMap = {
        webcam_capture: 'Webcam Capture',
        audio_alert: 'Audio Alert',
        tab_switch: 'Tab Switch Detected',
        fullscreen_exit: 'Fullscreen Exit',
        copy_paste_attempt: 'Copy/Paste Attempt',
        face_detection_failure: 'Face Detection Failure',
        suspicious_activity: 'Suspicious Activity'
    }
    
    return titleMap[eventType] || eventType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const getSeverityClass = (severity) => {
    switch (severity) {
        case 'critical':
            return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
        case 'warning':
            return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
        case 'info':
            return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
        default:
            return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
}

const getIconColor = (severity) => {
    switch (severity) {
        case 'critical':
            return 'text-red-500'
        case 'warning':
            return 'text-yellow-500'
        case 'info':
            return 'text-blue-500'
        default:
            return 'text-gray-500'
    }
}

const filteredEventData = computed(() => {
    if (!props.event.data) return {}
    
    // Filter out data that's already displayed in specific sections
    const excludeKeys = ['face_count', 'faces', 'audio_level', 'tab_title', 'action', 'content']
    
    return Object.fromEntries(
        Object.entries(props.event.data).filter(([key]) => !excludeKeys.includes(key))
    )
})

const formatDateTime = (timestamp) => {
    if (!timestamp) return 'N/A'
    
    const date = new Date(timestamp)
    return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    })
}

const formatKey = (key) => {
    return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const formatValue = (value) => {
    if (typeof value === 'object') {
        return JSON.stringify(value)
    }
    return String(value)
}

const isImageEvent = (eventType) => {
    return ['webcam_capture', 'screen_capture'].includes(eventType)
}

const isAudioEvent = (eventType) => {
    return ['audio_alert', 'audio_recording'].includes(eventType)
}

const getAudioLevelColor = (level) => {
    if (level > -20) return 'bg-red-500'
    if (level > -40) return 'bg-yellow-500'
    return 'bg-green-500'
}

const getAudioLevelPercentage = (level) => {
    // Convert dB level to percentage (assuming range from -80dB to 0dB)
    const minDb = -80
    const maxDb = 0
    const percentage = ((level - minDb) / (maxDb - minDb)) * 100
    return Math.max(0, Math.min(100, percentage))
}

const openMediaInNewTab = () => {
    if (props.event.media_url) {
        window.open(props.event.media_url, '_blank')
    }
}
</script>
