<template>
    <ParentTransition appear :visibility="true">
        <BaseCard>
            <!-- Header -->
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                        {{ $trans("exam.proctoring.review.title") || "Proctoring Review" }}
                    </h1>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                        {{ submission.studentName }} - {{ submission.admissionNumber }}
                    </p>
                    <p class="text-sm text-gray-500 dark:text-gray-500">
                        {{ submission.courseName }} {{ submission.batchName }}
                    </p>
                </div>
                <div class="flex items-center space-x-3">
                    <BaseButton
                        design="secondary"
                        @click="goBack"
                    >
                        <i class="fas fa-arrow-left mr-2"></i>
                        {{ $trans("general.back") || "Back" }}
                    </BaseButton>
                    <BaseButton
                        design="primary"
                        @click="viewSubmissionDetails"
                    >
                        <i class="fas fa-file-alt mr-2"></i>
                        {{ $trans("exam.online_exam.submission.view_answers") || "View Answers" }}
                    </BaseButton>
                </div>
            </div>

            <!-- Exam Information -->
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <BaseDataView label="Exam Title">
                        {{ submission.exam?.title || 'N/A' }}
                    </BaseDataView>
                    <BaseDataView label="Started At">
                        {{ submission.startedAt?.formatted || 'N/A' }}
                    </BaseDataView>
                    <BaseDataView label="Submitted At">
                        {{ submission.submittedAt?.formatted || 'N/A' }}
                    </BaseDataView>
                    <BaseDataView label="Duration">
                        {{ getExamDuration() }}
                    </BaseDataView>
                </div>
            </div>

            <!-- Proctoring Dashboard -->
            <ProctorReviewDashboard
                v-if="submission.uuid && submission.exam?.uuid"
                :exam-uuid="submission.exam.uuid"
                :submission-uuid="submission.uuid"
            />

            <!-- Loading State -->
            <div v-if="isLoading" class="flex justify-center py-12">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>

            <!-- Error State -->
            <div v-if="hasError" class="text-center py-12">
                <div class="text-red-500 mb-4">
                    <i class="fas fa-exclamation-triangle text-4xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    {{ $trans("exam.proctoring.review.load_error") || "Failed to load proctoring data" }}
                </h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                    {{ $trans("exam.proctoring.review.load_error_description") || "There was an error loading the proctoring information for this submission." }}
                </p>
                <BaseButton design="primary" @click="loadSubmission">
                    <i class="fas fa-sync-alt mr-2"></i>
                    {{ $trans("general.retry") || "Retry" }}
                </BaseButton>
            </div>

            <!-- No Proctoring State -->
            <div v-if="!isLoading && !hasError && submission.exam && !submission.exam.enableProctoring" class="text-center py-12">
                <div class="text-gray-400 mb-4">
                    <i class="fas fa-shield-alt text-4xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    {{ $trans("exam.proctoring.review.not_enabled") || "Proctoring Not Enabled" }}
                </h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                    {{ $trans("exam.proctoring.review.not_enabled_description") || "This exam was not configured with proctoring features." }}
                </p>
                <BaseButton design="secondary" @click="viewSubmissionDetails">
                    <i class="fas fa-file-alt mr-2"></i>
                    {{ $trans("exam.online_exam.submission.view_answers") || "View Answers" }}
                </BaseButton>
            </div>
        </BaseCard>
    </ParentTransition>
</template>

<script>
export default {
    name: "ExamOnlineExamSubmissionProctorReview",
}
</script>

<script setup>
import { ref, reactive, onMounted, computed } from "vue"
import { useRoute, useRouter } from "vue-router"
import { useStore } from "vuex"
import ProctorReviewDashboard from "@core/components/Exam/ProctorReviewDashboard.vue"

const route = useRoute()
const router = useRouter()
const store = useStore()

// State
const isLoading = ref(true)
const hasError = ref(false)
const submission = reactive({})

// Computed
const examDuration = computed(() => {
    if (!submission.startedAt?.value || !submission.submittedAt?.value) {
        return 'N/A'
    }
    
    const start = new Date(submission.startedAt.value)
    const end = new Date(submission.submittedAt.value)
    const durationMs = end - start
    const durationMinutes = Math.floor(durationMs / (1000 * 60))
    
    const hours = Math.floor(durationMinutes / 60)
    const minutes = durationMinutes % 60
    
    if (hours > 0) {
        return `${hours}h ${minutes}m`
    } else {
        return `${minutes}m`
    }
})

// Methods
const loadSubmission = async () => {
    isLoading.value = true
    hasError.value = false
    
    try {
        const response = await store.dispatch("exam/onlineExam/submission/getQuestions", {
            uuid: route.params.uuid,
            moduleUuid: route.params.submissionUuid,
        })

        
        const responseData = { ...response }
        console.log('Response Data:', responseData)
        if (responseData.exam && responseData.exam.hasOwnProperty('enableProctoring')) {
            // Ensure enableProctoring is a proper boolean
            responseData.exam.enableProctoring = Boolean(Number(responseData.exam.enableProctoring))
        }
        Object.assign(submission, responseData)
        
        // Check if proctoring is enabled
        if (!submission.exam?.enableProctoring) {
            console.warn('Proctoring not enabled for this exam')
        }
        
    } catch (error) {
        console.error('Failed to load submission:', error)
        hasError.value = true
    } finally {
        isLoading.value = false
    }
}

const getExamDuration = () => {
    return examDuration.value
}

const goBack = () => {
    router.push({
        name: "ExamOnlineExamShow",
        params: { uuid: route.params.uuid }
    })
}

const viewSubmissionDetails = () => {
    router.push({
        name: "ExamOnlineExamSubmissionList"
    })
    return
}

// Lifecycle
onMounted(() => {
    loadSubmission()
})
</script>
