<template>
    <ListItem :init-url="initUrl" @setItems="setItems">
        <template #header>
            <PageHeader
                :title="$trans('exam.online_exam.online_exam')"
                :navs="[{ label: $trans('exam.exam'), path: 'Exam' }]"
            >
                <PageHeaderAction
                    url="exam/online-exams/"
                    name="ExamOnlineExam"
                    :title="$trans('exam.online_exam.online_exam')"
                    :actions="userActions"
                    :dropdown-actions="dropdownActions"
                    @toggleFilter="showFilter = !showFilter"
                />
            </PageHeader>
        </template>

        <template #filter>
            <ParentTransition appear :visibility="showFilter">
                <FilterForm
                    @refresh="emitter.emit('listItems')"
                    @hide="showFilter = false"
                ></FilterForm>
            </ParentTransition>
        </template>

        <ParentTransition
            appear
            :visibility="true"
            v-if="actingAs(['student', 'guardian'], 'any')"
        >
            <CardList :header="onlineExams.headers" :meta="onlineExams.meta">
                <div class="grid grid-cols-1 gap-4 px-4 pt-4 lg:grid-cols-2">
                    <template
                        v-for="onlineExam in onlineExams.data"
                        :key="onlineExam.uuid"
                    >
                        <CardView no-padding>
                            <div class="text-center dark:text-gray-400">
                                {{ onlineExam.title }}
                                <span class="text-sm" v-if="onlineExam.type"
                                    >({{ onlineExam.type?.label }})</span
                                >
                                <!-- Submit Button -->
                                <span
                                    v-if="
                                        actingAs('student') &&
                                        onlineExam.shouldShowSubmitButton
                                    "
                                    class="cursor-pointer ml-2"
                                    @click="
                                        router.push({
                                            name: 'ExamOnlineExamSubmit',
                                            params: { uuid: onlineExam.uuid },
                                        })
                                    "
                                    v-tooltip="
                                        onlineExam.hasSubmission && !onlineExam.submittedAt.value
                                            ? $trans('exam.online_exam.continue_exam')
                                            : $trans('exam.online_exam.submit')
                                    "
                                    ><i
                                        class="fas fa-arrow-up-right-from-square text-blue-600"
                                    ></i
                                ></span>

                                <!-- Show Show Continue Button-->
                                <span
                                    v-if="
                                        actingAs('student') &&
                                        onlineExam.hasSubmission && !onlineExam.submittedAt.value
                                    "
                                    class="cursor-pointer ml-2"
                                    @click="
                                        router.push({
                                            name: 'ExamOnlineExamSubmit',
                                            params: { uuid: onlineExam.uuid },
                                        })
                                    "
                                    v-tooltip="
                                        onlineExam.hasSubmission && !onlineExam.submittedAt.value
                                            ? $trans('exam.online_exam.continue_exam')
                                            : $trans('exam.online_exam.submit')
                                    "
                                    ><i
                                        class="fas fa-arrow-up-right-from-square text-blue-600"
                                    ></i
                                ></span>

                                <!-- Result Button -->
                                <span
                                    v-if="
                                        actingAs(['student', 'guardian'], 'any') &&
                                        onlineExam.shouldShowResultButton
                                    "
                                    class="cursor-pointer ml-2"
                                    @click="
                                        router.push({
                                            name: 'ExamOnlineExamStudentSubmission',
                                            params: { uuid: onlineExam.uuid },
                                        })
                                    "
                                    v-tooltip="
                                        $trans('exam.online_exam.result')
                                    "
                                    ><i
                                        class="fas fa-arrow-up-right-from-square text-green-600"
                                    ></i
                                ></span>

                                <!-- Processing Results Message -->
                                <span
                                    v-if="
                                        actingAs('student') &&
                                        onlineExam.hasSubmission &&
                                        onlineExam.submittedAt?.value &&
                                        !onlineExam.resultPublishedAt?.value &&
                                        onlineExam.isFlexibleTiming &&
                                        onlineExam.autoPublishResultsForFlexibleTiming
                                    "
                                    class="ml-2 text-sm text-yellow-600"
                                    v-tooltip="
                                        $trans('exam.online_exam.processing_results')
                                    "
                                    ><i class="fas fa-clock"></i
                                ></span>

                                <!-- Results Pending Message -->
                                <span
                                    v-if="
                                        actingAs('student') &&
                                        onlineExam.hasSubmission &&
                                        onlineExam.submittedAt?.value &&
                                        !onlineExam.resultPublishedAt?.value &&
                                        (!onlineExam.isFlexibleTiming || !onlineExam.autoPublishResultsForFlexibleTiming)
                                    "
                                    class="ml-2 text-sm text-gray-500"
                                    v-tooltip="
                                        $trans('exam.online_exam.results_pending')
                                    "
                                    ><i class="fas fa-hourglass-half"></i
                                ></span>
                            </div>

                            <div class="text-sm text-center dark:text-gray-500">
                                <!-- Flexible Timing Status -->
                                <template v-if="onlineExam.isFlexibleTiming">
                                    <BaseBadge
                                        design="success"
                                        v-if="onlineExam.isAvailable"
                                        >{{
                                            $trans("exam.online_exam.available")
                                        }}</BaseBadge
                                    >
                                    <BaseBadge
                                        design="danger"
                                        v-else-if="onlineExam.expiryDateTime && new Date() > new Date(onlineExam.expiryDateTime)"
                                        >{{
                                            $trans("exam.online_exam.expired")
                                        }}</BaseBadge
                                    >
                                    <BaseBadge
                                        design="secondary"
                                        v-else
                                        >{{
                                            $trans("exam.online_exam.not_available")
                                        }}</BaseBadge
                                    >
                                </template>

                                <!-- Traditional Timing Status -->
                                <template v-else>
                                    <BaseBadge
                                        design="info"
                                        v-if="
                                            onlineExam.isUpcoming &&
                                            onlineExam.timeLeft >
                                                onlineExam.upcomingThreshold
                                        "
                                        >{{
                                            $trans("exam.online_exam.upcoming")
                                        }}</BaseBadge
                                    >
                                    <BaseBadge
                                        design="info"
                                        v-else-if="
                                            onlineExam.isUpcoming &&
                                            onlineExam.timeLeft <=
                                                onlineExam.upcomingThreshold
                                        "
                                        >{{
                                            $trans("exam.online_exam.starting_in", {
                                                attribute: onlineExam.timeLeft,
                                            })
                                        }}</BaseBadge
                                    >
                                    <BaseBadge
                                        design="success"
                                        v-else-if="onlineExam.isLive"
                                        >{{
                                            $trans("exam.online_exam.live")
                                        }}</BaseBadge
                                    >
                                    <BaseBadge
                                        design="primary"
                                        v-else-if="onlineExam.isCompleted"
                                        >{{
                                            $trans("exam.online_exam.completed")
                                        }}</BaseBadge
                                    >
                                </template>
                            </div>
                            <div class="text-sm text-center dark:text-gray-500">
                                <template v-if="onlineExam.isFlexibleTiming">
                                    <div>{{ $trans("exam.online_exam.flexible_timing") }}</div>
                                    <div>Duration: {{ onlineExam.duration }}</div>
                                    <div v-if="onlineExam.expiryDate">
                                        Expires: {{ onlineExam.expiryDate.formatted }}
                                        <span v-if="onlineExam.expiryTime">{{ onlineExam.expiryTime.at }}</span>
                                    </div>
                                </template>
                                <template v-else>
                                    {{ onlineExam.date.formatted }}
                                    {{ onlineExam.period }} ({{
                                        onlineExam.duration
                                    }})
                                </template>
                            </div>

                            <div class="flex justify-between"></div>

                            <SimpleTable
                                :header="recordHeader"
                                v-if="onlineExam.records.length > 0"
                            >
                                <DataRow
                                    v-for="record in onlineExam.records"
                                    :key="record.uuid"
                                >
                                    <DataCell name="course">
                                        {{
                                            record.batch.course?.name +
                                            " " +
                                            record.batch.name
                                        }}
                                    </DataCell>
                                    <DataCell name="subject">
                                        {{ record.subject.name }}
                                    </DataCell>
                                </DataRow>
                            </SimpleTable>
                        </CardView>
                    </template>
                </div>
                <div>
                    <Pagination
                        card-view
                        :meta="onlineExams.meta"
                        @refresh="emitter.emit('listItems')"
                    ></Pagination>
                </div>
                <template #content>
                    <div class="text-xl font-semibold">
                        {{ $trans("dashboard.nothing_to_show") }}
                    </div>
                </template>
            </CardList>
        </ParentTransition>

        <ParentTransition appear :visibility="true" v-else>
            <DataTable
                :header="onlineExams.headers"
                :meta="onlineExams.meta"
                module="exam.online_exam"
                @refresh="emitter.emit('listItems')"
            >
                <DataRow
                    v-for="onlineExam in onlineExams.data"
                    :key="onlineExam.uuid"
                    @double-click="
                        router.push({
                            name: 'ExamOnlineExamShow',
                            params: { uuid: onlineExam.uuid },
                        })
                    "
                >
                    <DataCell name="title">
                        {{ onlineExam.title }}
                        <TextMuted block>{{
                            onlineExam.type?.label
                        }}</TextMuted>
                    </DataCell>
                    <DataCell name="records">
                        <div v-for="record in onlineExam.records">
                            {{
                                record.batch.course?.name +
                                " " +
                                record.batch.name
                            }}
                            <TextMuted v-if="record.subject">{{
                                record.subject.name
                            }}</TextMuted>
                        </div>
                    </DataCell>
                    <DataCell name="date">
                        <template v-if="onlineExam.isFlexibleTiming">
                            <div>{{ $trans("exam.online_exam.flexible_timing") }}</div>
                            <TextMuted block v-if="onlineExam.expiryDate">
                                Expires: {{ onlineExam.expiryDate.formatted }}
                                <span v-if="onlineExam.expiryTime">{{ onlineExam.expiryTime.at }}</span>
                            </TextMuted>
                        </template>
                        <template v-else>
                            {{ onlineExam.date.formatted }}
                            <TextMuted block>{{
                                onlineExam.endDate.formatted
                            }}</TextMuted>
                        </template>
                        <div>
                            <!-- Flexible Timing Status -->
                            <template v-if="onlineExam.isFlexibleTiming">
                                <BaseBadge
                                    design="success"
                                    v-if="onlineExam.isAvailable"
                                    >{{
                                        $trans("exam.online_exam.available")
                                    }}</BaseBadge
                                >
                                <BaseBadge
                                    design="danger"
                                    v-else-if="onlineExam.expiryDateTime && new Date() > new Date(onlineExam.expiryDateTime)"
                                    >{{
                                        $trans("exam.online_exam.expired")
                                    }}</BaseBadge
                                >
                                <BaseBadge
                                    design="secondary"
                                    v-else
                                    >{{
                                        $trans("exam.online_exam.not_available")
                                    }}</BaseBadge
                                >
                            </template>
                            <!-- Traditional Exam Status -->
                            <template v-else>
                                <BaseBadge
                                    design="info"
                                    v-if="
                                        onlineExam.isUpcoming &&
                                        onlineExam.timeLeft >
                                            onlineExam.upcomingThreshold
                                    "
                                    >{{
                                        $trans("exam.online_exam.upcoming")
                                    }}</BaseBadge
                                >
                                <BaseBadge
                                    design="info"
                                    v-else-if="
                                        onlineExam.isUpcoming &&
                                        onlineExam.timeLeft <=
                                            onlineExam.upcomingThreshold
                                    "
                                    >{{
                                        $trans("exam.online_exam.starting_in", {
                                            attribute: onlineExam.timeLeft,
                                        })
                                    }}</BaseBadge
                                >
                                <BaseBadge
                                    design="success"
                                    v-else-if="onlineExam.isLive"
                                    >{{
                                        $trans("exam.online_exam.live")
                                    }}</BaseBadge
                                >
                                <BaseBadge
                                    design="primary"
                                    v-else-if="onlineExam.isCompleted"
                                    >{{
                                        $trans("exam.online_exam.completed")
                                    }}</BaseBadge
                                >
                            </template>
                        </div>
                    </DataCell>
                    <DataCell name="time">
                        <template v-if="onlineExam.isFlexibleTiming">
                            <div>{{ onlineExam.duration }}</div>
                            <TextMuted block>Individual Duration</TextMuted>
                        </template>
                        <template v-else>
                            {{ onlineExam.period }}
                            <TextMuted block>{{ onlineExam.duration }}</TextMuted>
                        </template>
                    </DataCell>
                    <DataCell name="employee">
                        {{ onlineExam.employee?.name || "-" }}
                        <TextMuted block>{{
                            onlineExam.employee?.codeNumber
                        }}</TextMuted>
                    </DataCell>
                    <DataCell name="createdAt">
                        {{ onlineExam.createdAt.formatted }}
                    </DataCell>
                    <DataCell name="action">
                        <FloatingMenu>
                            <FloatingMenuItem
                                icon="fas fa-arrow-circle-right"
                                @click="
                                    router.push({
                                        name: 'ExamOnlineExamShow',
                                        params: { uuid: onlineExam.uuid },
                                    })
                                "
                                >{{ $trans("general.show") }}</FloatingMenuItem
                            >
                            <FloatingMenuItem
                                v-if="
                                    perform('online-exam:edit') &&
                                    onlineExam.isEditable
                                "
                                icon="fas fa-edit"
                                @click="
                                    router.push({
                                        name: 'ExamOnlineExamEdit',
                                        params: { uuid: onlineExam.uuid },
                                    })
                                "
                                >{{ $trans("general.edit") }}</FloatingMenuItem
                            >
                            <FloatingMenuItem
                                v-if="perform('online-exam:create')"
                                icon="fas fa-copy"
                                @click="
                                    router.push({
                                        name: 'ExamOnlineExamDuplicate',
                                        params: { uuid: onlineExam.uuid },
                                    })
                                "
                                >{{
                                    $trans("general.duplicate")
                                }}</FloatingMenuItem
                            >
                            <FloatingMenuItem
                                v-if="
                                    perform('online-exam:delete') &&
                                    onlineExam.isDeletable
                                "
                                icon="fas fa-trash"
                                @click="
                                    emitter.emit('deleteItem', {
                                        uuid: onlineExam.uuid,
                                    })
                                "
                                >{{
                                    $trans("general.delete")
                                }}</FloatingMenuItem
                            >
                        </FloatingMenu>
                    </DataCell>
                </DataRow>
                <template #actionButton>
                    <BaseButton
                        v-if="perform('online-exam:create')"
                        @click="router.push({ name: 'ExamOnlineExamCreate' })"
                    >
                        {{
                            $trans("global.add", {
                                attribute: $trans(
                                    "exam.online_exam.online_exam"
                                ),
                            })
                        }}
                    </BaseButton>
                </template>
            </DataTable>
        </ParentTransition>
    </ListItem>
</template>

<script>
export default {
    name: "ExamOnlineExamList",
}
</script>

<script setup>
import { ref, reactive, inject } from "vue"
import { useRouter } from "vue-router"
import { perform, actingAs } from "@core/helpers/action"
import FilterForm from "./Filter.vue"

const router = useRouter()

const emitter = inject("emitter")
const $trans = inject("$trans")

let userActions = ["filter"]
if (perform("online-exam:create")) {
    userActions.unshift("create")
}

let dropdownActions = []
if (perform("online-exam:export")) {
    dropdownActions = ["print", "pdf", "excel"]
}

const recordHeader = [
    {
        key: "course",
        label: $trans("academic.course.course"),
        visibility: true,
    },
    {
        key: "subject",
        label: $trans("academic.subject.subject"),
        visibility: true,
    },
]

const initUrl = "exam/onlineExam/"
const showFilter = ref(false)

const onlineExams = reactive({})

const setItems = (data) => {
    Object.assign(onlineExams, data)
}
</script>
