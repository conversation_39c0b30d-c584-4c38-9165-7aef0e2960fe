<template>
    <div v-if="isActive" class="fixed bottom-4 right-4 z-40">
        <!-- Minimized View -->
        <div
            v-if="isMinimized"
            @click="isMinimized = false"
            class="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-3 cursor-pointer hover:shadow-xl transition-shadow"
        >
            <div class="flex items-center space-x-2">
                <div class="w-3 h-3 rounded-full" :class="overallStatusColor"></div>
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {{ $trans("exam.proctoring.monitoring") || "Monitoring" }}
                </span>
                <span v-if="totalViolations > 0" class="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">
                    {{ totalViolations }}
                </span>
            </div>
        </div>

        <!-- Expanded View -->
        <div
            v-else
            class="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4 w-80"
        >
            <!-- Header -->
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    {{ $trans("exam.proctoring.dashboard") || "Proctoring Dashboard" }}
                </h3>
                <button
                    @click="isMinimized = true"
                    class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>

            <!-- Overall Status -->
            <div class="mb-4 p-3 rounded-lg" :class="overallStatusBg">
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 rounded-full" :class="overallStatusColor"></div>
                    <span class="text-sm font-medium" :class="overallStatusText">
                        {{ overallStatusMessage }}
                    </span>
                </div>
            </div>

            <!-- Individual Status Items -->
            <div class="space-y-3">
                <!-- Webcam Status -->
                <ProctorStatusItem
                    v-if="config.webcamMonitoring"
                    icon="webcam"
                    :label="$trans('exam.proctoring.webcam') || 'Webcam'"
                    :status="status.webcamStatus"
                    :count="eventCounts.webcamCaptures"
                    :details="$trans('exam.proctoring.captures_taken') || 'captures taken'"
                />

                <!-- Microphone Status -->
                <ProctorStatusItem
                    v-if="config.microphoneMonitoring"
                    icon="microphone"
                    :label="$trans('exam.proctoring.microphone') || 'Microphone'"
                    :status="status.microphoneStatus"
                    :count="eventCounts.audioAlerts"
                    :details="$trans('exam.proctoring.audio_alerts') || 'audio alerts'"
                />

                <!-- Face Detection Status -->
                <ProctorStatusItem
                    v-if="config.faceDetection"
                    icon="face"
                    :label="$trans('exam.proctoring.face_detection') || 'Face Detection'"
                    :status="status.faceDetectionStatus"
                    :count="eventCounts.faceDetectionFailures"
                    :details="$trans('exam.proctoring.detection_failures') || 'detection failures'"
                />

                <!-- Fullscreen Status -->
                <ProctorStatusItem
                    v-if="config.fullscreenEnforcement"
                    icon="fullscreen"
                    :label="$trans('exam.proctoring.fullscreen') || 'Fullscreen'"
                    :status="status.fullscreenStatus"
                    :count="eventCounts.fullscreenExits"
                    :details="$trans('exam.proctoring.exits_detected') || 'exits detected'"
                />

                <!-- Security Violations -->
                <ProctorStatusItem
                    icon="security"
                    :label="$trans('exam.proctoring.security') || 'Security'"
                    :status="securityStatus"
                    :count="securityViolations"
                    :details="$trans('exam.proctoring.violations_detected') || 'violations detected'"
                />
            </div>

            <!-- Actions -->
            <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
                <div class="flex space-x-2">
                    <BaseButton
                        size="xs"
                        design="secondary"
                        @click="$emit('test-camera')"
                    >
                        {{ $trans("exam.proctoring.test_camera") || "Test Camera" }}
                    </BaseButton>
                    <BaseButton
                        size="xs"
                        design="secondary"
                        @click="$emit('test-microphone')"
                    >
                        {{ $trans("exam.proctoring.test_mic") || "Test Mic" }}
                    </BaseButton>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "ProctorDashboard",
}
</script>

<script setup>
import { ref, computed } from 'vue'
import ProctorStatusItem from './ProctorStatusItem.vue'

defineEmits(['test-camera', 'test-microphone'])

const props = defineProps({
    isActive: {
        type: Boolean,
        default: false
    },
    config: {
        type: Object,
        default: () => ({})
    },
    status: {
        type: Object,
        default: () => ({
            webcamStatus: 'inactive',
            microphoneStatus: 'inactive',
            faceDetectionStatus: 'inactive',
            fullscreenStatus: 'inactive'
        })
    },
    eventCounts: {
        type: Object,
        default: () => ({
            webcamCaptures: 0,
            audioAlerts: 0,
            tabSwitches: 0,
            fullscreenExits: 0,
            copyPasteAttempts: 0,
            faceDetectionFailures: 0
        })
    }
})

const isMinimized = ref(true)

const totalViolations = computed(() => {
    return props.eventCounts.tabSwitches + 
           props.eventCounts.fullscreenExits + 
           props.eventCounts.copyPasteAttempts + 
           props.eventCounts.faceDetectionFailures
})

const securityViolations = computed(() => {
    return props.eventCounts.tabSwitches + props.eventCounts.copyPasteAttempts
})

const securityStatus = computed(() => {
    if (securityViolations.value > 5) return 'error'
    if (securityViolations.value > 0) return 'warning'
    return 'active'
})

const overallStatus = computed(() => {
    const statuses = Object.values(props.status)
    
    if (statuses.includes('error') || totalViolations.value > 10) {
        return 'error'
    }
    
    if (statuses.includes('warning') || totalViolations.value > 3) {
        return 'warning'
    }
    
    if (statuses.every(status => status === 'active')) {
        return 'active'
    }
    
    return 'ready'
})

const overallStatusColor = computed(() => {
    switch (overallStatus.value) {
        case 'active': return 'bg-green-500'
        case 'warning': return 'bg-yellow-500'
        case 'error': return 'bg-red-500'
        default: return 'bg-gray-400'
    }
})

const overallStatusBg = computed(() => {
    switch (overallStatus.value) {
        case 'active': return 'bg-green-50 dark:bg-green-900/20'
        case 'warning': return 'bg-yellow-50 dark:bg-yellow-900/20'
        case 'error': return 'bg-red-50 dark:bg-red-900/20'
        default: return 'bg-gray-50 dark:bg-gray-900/20'
    }
})

const overallStatusText = computed(() => {
    switch (overallStatus.value) {
        case 'active': return 'text-green-800 dark:text-green-200'
        case 'warning': return 'text-yellow-800 dark:text-yellow-200'
        case 'error': return 'text-red-800 dark:text-red-200'
        default: return 'text-gray-800 dark:text-gray-200'
    }
})

const overallStatusMessage = computed(() => {
    switch (overallStatus.value) {
        case 'active': 
            return 'All systems operational'
        case 'warning': 
            return `${totalViolations.value} violation${totalViolations.value !== 1 ? 's' : ''} detected`
        case 'error': 
            return `${totalViolations.value} violations - Review required`
        default: 
            return 'Proctoring ready'
    }
})
</script>
