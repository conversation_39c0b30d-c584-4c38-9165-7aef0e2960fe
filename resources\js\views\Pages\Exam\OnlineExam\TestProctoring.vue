<template>
    <div class="p-6">
        <h1 class="text-2xl font-bold mb-6">Proctoring Configuration Test</h1>
        
        <!-- Test Configuration Panel -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-lg font-semibold mb-4">Test Configuration</h2>
            <ProctorConfigurationPanel v-model="testConfig" />
        </div>

        <!-- Test Results -->
        <div class="bg-gray-50 rounded-lg p-6">
            <h2 class="text-lg font-semibold mb-4">Configuration Output</h2>
            <pre class="bg-gray-800 text-green-400 p-4 rounded text-sm overflow-auto">{{ JSON.stringify(testConfig, null, 2) }}</pre>
        </div>

        <!-- Test Requirements Check -->
        <div class="mt-6">
            <BaseButton @click="showRequirementsCheck = true" design="primary">
                Test Requirements Check
            </BaseButton>
        </div>

        <!-- Requirements Check Modal -->
        <ProctorRequirementsCheck
            :visibility="showRequirementsCheck"
            :exam="mockExam"
            @close="showRequirementsCheck = false"
            @requirements-met="onRequirementsMet"
        />
    </div>
</template>

<script>
export default {
    name: "ExamOnlineExamTestProctoring",
}
</script>

<script setup>
import { ref, reactive, computed } from 'vue'
import ProctorConfigurationPanel from '@core/components/Exam/ProctorConfigurationPanel.vue'
import ProctorRequirementsCheck from '@core/components/Exam/ProctorRequirementsCheck.vue'

// Test configuration
const testConfig = reactive({
    enableProctoring: false,
    proctorConfig: {}
})

const showRequirementsCheck = ref(false)

// Mock exam for testing requirements
const mockExam = computed(() => ({
    uuid: 'test-exam-uuid',
    title: 'Test Exam',
    enableProctoring: testConfig.enableProctoring,
    proctorConfig: testConfig.proctorConfig
}))

const onRequirementsMet = () => {
    showRequirementsCheck.value = false
    alert('Requirements met! Exam would start now.')
}
</script>
