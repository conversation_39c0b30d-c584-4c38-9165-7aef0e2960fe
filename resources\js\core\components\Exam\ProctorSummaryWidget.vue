<template>
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-4">
        <!-- Header -->
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                {{ $trans("exam.proctoring.summary.title") || "Proctoring Summary" }}
            </h3>
            <div class="flex items-center space-x-2">
                <span 
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    :class="overallStatusClass"
                >
                    {{ overallStatusText }}
                </span>
                <BaseButton
                    v-if="showViewButton"
                    size="xs"
                    design="primary"
                    @click="$emit('view-details')"
                >
                    {{ $trans("general.view") || "View" }}
                </BaseButton>
            </div>
        </div>

        <!-- Loading State -->
        <div v-if="isLoading" class="flex justify-center py-4">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        </div>

        <!-- Summary Stats -->
        <div v-else-if="summary" class="grid grid-cols-2 md:grid-cols-4 gap-3">
            <div class="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="text-xl font-bold text-blue-600 dark:text-blue-400">
                    {{ summary.totalEvents || 0 }}
                </div>
                <div class="text-xs text-gray-600 dark:text-gray-400">
                    {{ $trans("exam.proctoring.summary.total") || "Total" }}
                </div>
            </div>
            <div class="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="text-xl font-bold text-red-600 dark:text-red-400">
                    {{ summary.criticalEvents || 0 }}
                </div>
                <div class="text-xs text-gray-600 dark:text-gray-400">
                    {{ $trans("exam.proctoring.summary.critical") || "Critical" }}
                </div>
            </div>
            <div class="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="text-xl font-bold text-yellow-600 dark:text-yellow-400">
                    {{ summary.warningEvents || 0 }}
                </div>
                <div class="text-xs text-gray-600 dark:text-gray-400">
                    {{ $trans("exam.proctoring.summary.warnings") || "Warnings" }}
                </div>
            </div>
            <div class="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="text-xl font-bold text-green-600 dark:text-green-400">
                    {{ summary.infoEvents || 0 }}
                </div>
                <div class="text-xs text-gray-600 dark:text-gray-400">
                    {{ $trans("exam.proctoring.summary.info") || "Info" }}
                </div>
            </div>
        </div>

        <!-- Event Type Breakdown -->
        <div v-if="summary && summary.eventTypes && Object.keys(summary.eventTypes).length > 0" class="mt-4">
            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">
                {{ $trans("exam.proctoring.summary.event_breakdown") || "Event Breakdown" }}
            </h4>
            <div class="space-y-2">
                <div 
                    v-for="(count, eventType) in summary.eventTypes" 
                    :key="eventType"
                    class="flex items-center justify-between text-sm"
                >
                    <div class="flex items-center space-x-2">
                        <component :is="getEventIcon(eventType)" class="w-4 h-4 text-gray-500" />
                        <span class="text-gray-700 dark:text-gray-300">
                            {{ getEventTitle(eventType) }}
                        </span>
                    </div>
                    <span class="font-medium text-gray-900 dark:text-white">
                        {{ count }}
                    </span>
                </div>
            </div>
        </div>

        <!-- Recent Events -->
        <div v-if="summary && summary.timeline && summary.timeline.length > 0" class="mt-4">
            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">
                {{ $trans("exam.proctoring.summary.recent_events") || "Recent Events" }}
            </h4>
            <div class="space-y-2 max-h-32 overflow-y-auto">
                <div 
                    v-for="event in summary.timeline.slice(-5)" 
                    :key="event.time"
                    class="flex items-center justify-between text-xs"
                >
                    <div class="flex items-center space-x-2">
                        <span
                            class="w-2 h-2 rounded-full"
                            :class="getSeverityDotClass(event.severity)"
                        ></span>
                        <span class="text-gray-600 dark:text-gray-400">
                            {{ event.time }}
                        </span>
                        <span class="text-gray-700 dark:text-gray-300">
                            {{ event.description }}
                        </span>
                    </div>
                    <span v-if="event.hasMedia" class="text-blue-500">
                        <i class="fas fa-paperclip"></i>
                    </span>
                </div>
            </div>
        </div>

        <!-- No Data State -->
        <div v-else-if="!isLoading && (!summary || summary.totalEvents === 0)" class="text-center py-4">
            <div class="text-gray-400 mb-2">
                <i class="fas fa-shield-alt text-2xl"></i>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400">
                {{ $trans("exam.proctoring.summary.no_events") || "No proctoring events recorded" }}
            </p>
        </div>
    </div>
</template>

<script>
export default {
    name: "ProctorSummaryWidget",
}
</script>

<script setup>
import { computed } from 'vue'

defineEmits(['view-details'])

const props = defineProps({
    summary: {
        type: Object,
        default: null
    },
    isLoading: {
        type: Boolean,
        default: false
    },
    showViewButton: {
        type: Boolean,
        default: true
    }
})

// Computed
const overallStatusClass = computed(() => {
    if (!props.summary) return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    
    const criticalCount = props.summary.criticalEvents || 0
    const warningCount = props.summary.warningEvents || 0

    if (criticalCount > 0) {
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
    } else if (warningCount > 0) {
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
    } else {
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
    }
})

const overallStatusText = computed(() => {
    if (!props.summary) return 'Unknown'
    
    const criticalCount = props.summary.criticalEvents || 0
    const warningCount = props.summary.warningEvents || 0

    if (criticalCount > 0) {
        return 'Requires Review'
    } else if (warningCount > 0) {
        return 'Minor Issues'
    } else {
        return 'Clean Session'
    }
})

// Helper functions
const getEventIcon = (eventType) => {
    const iconMap = {
        webcam_capture: {
            template: `<svg fill="currentColor" viewBox="0 0 20 20"><path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"></path></svg>`
        },
        audio_alert: {
            template: `<svg fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clip-rule="evenodd"></path></svg>`
        },
        tab_switch: {
            template: `<svg fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4z" clip-rule="evenodd"></path></svg>`
        },
        fullscreen_exit: {
            template: `<svg fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4z" clip-rule="evenodd"></path></svg>`
        },
        copy_paste_attempt: {
            template: `<svg fill="currentColor" viewBox="0 0 20 20"><path d="M8 2a1 1 0 000 2h2a1 1 0 100-2H8z"></path><path d="M3 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6h-4.586l1.293-1.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L10.414 13H15v3a2 2 0 01-2 2H5a2 2 0 01-2-2V5z"></path></svg>`
        },
        face_detection_failure: {
            template: `<svg fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"></path></svg>`
        },
        suspicious_activity: {
            template: `<svg fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>`
        }
    }
    
    return iconMap[eventType] || iconMap.suspicious_activity
}

const getEventTitle = (eventType) => {
    const titleMap = {
        webcam_capture: 'Webcam Capture',
        audio_alert: 'Audio Alert',
        tab_switch: 'Tab Switch',
        fullscreen_exit: 'Fullscreen Exit',
        copy_paste_attempt: 'Copy/Paste Attempt',
        face_detection_failure: 'Face Detection Failure',
        suspicious_activity: 'Suspicious Activity'
    }
    
    return titleMap[eventType] || eventType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const getSeverityDotClass = (severity) => {
    switch (severity) {
        case 'critical':
            return 'bg-red-500'
        case 'warning':
            return 'bg-yellow-500'
        case 'info':
            return 'bg-blue-500'
        default:
            return 'bg-gray-400'
    }
}
</script>
