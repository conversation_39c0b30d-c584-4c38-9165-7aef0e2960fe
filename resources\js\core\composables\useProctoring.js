import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import * as faceapi from 'face-api.js'
import RecordRTC from 'recordrtc'

export function useProctoring(examUuid, submissionUuid, proctorConfig) {
    const store = useStore()
    
    // State
    const isInitialized = ref(false)
    const isActive = ref(false)
    const errors = ref([])
    
    // Media streams
    const videoStream = ref(null)
    const audioStream = ref(null)
    const screenRecorder = ref(null)
    const audioRecorder = ref(null)
    
    // Face detection
    const faceDetectionInterval = ref(null)
    const lastFaceDetection = ref(null)
    const consecutiveNoFaceCount = ref(0)
    
    // Monitoring intervals
    const webcamCaptureInterval = ref(null)
    const audioMonitoringInterval = ref(null)
    
    // Audio monitoring
    const audioContext = ref(null)
    const audioAnalyser = ref(null)
    const audioDataArray = ref(null)
    
    // Event tracking
    const eventCounts = reactive({
        webcamCaptures: 0,
        audioAlerts: 0,
        tabSwitches: 0,
        fullscreenExits: 0,
        copyPasteAttempts: 0,
        faceDetectionFailures: 0
    })

    /**
     * Initialize proctoring system
     */
    const initialize = async () => {
        try {
            errors.value = []
            
            // Load face-api models
            if (proctorConfig.face_detection) {
                await loadFaceApiModels()
            }
            
            // Initialize media streams
            if (proctorConfig.webcam_monitoring) {
                await initializeWebcam()
            }
            
            if (proctorConfig.microphone_monitoring) {
                await initializeMicrophone()
            }
            
            if (proctorConfig.screen_recording) {
                await initializeScreenRecording()
            }
            
            // Set up event listeners
            setupEventListeners()
            
            isInitialized.value = true
            
        } catch (error) {
            console.error('Failed to initialize proctoring:', error)
            errors.value.push(`Initialization failed: ${error.message}`)
            throw error
        }
    }

    /**
     * Start proctoring monitoring
     */
    const startMonitoring = () => {
        if (!isInitialized.value) {
            throw new Error('Proctoring not initialized')
        }
        
        isActive.value = true
        
        // Start webcam monitoring
        if (proctorConfig.webcam_monitoring && videoStream.value) {
            startWebcamCapture()
        }
        
        // Start audio monitoring
        if (proctorConfig.microphone_monitoring && audioStream.value) {
            startAudioMonitoring()
        }
        
        // Start face detection
        if (proctorConfig.face_detection && videoStream.value) {
            startFaceDetection()
        }
        
        // Start screen recording
        if (proctorConfig.screen_recording && screenRecorder.value) {
            screenRecorder.value.startRecording()
        }
        
        // Enforce fullscreen if required
        if (proctorConfig.fullscreen_enforcement) {
            enforceFullscreen()
        }
        
        // Block copy/paste if required
        if (proctorConfig.copy_paste_blocking) {
            blockCopyPaste()
        }
    }

    /**
     * Stop proctoring monitoring
     */
    const stopMonitoring = () => {
        isActive.value = false
        
        // Clear intervals
        if (webcamCaptureInterval.value) {
            clearInterval(webcamCaptureInterval.value)
        }
        
        if (audioMonitoringInterval.value) {
            clearInterval(audioMonitoringInterval.value)
        }
        
        if (faceDetectionInterval.value) {
            clearInterval(faceDetectionInterval.value)
        }
        
        // Stop recordings
        if (screenRecorder.value) {
            screenRecorder.value.stopRecording()
        }
        
        if (audioRecorder.value) {
            audioRecorder.value.stopRecording()
        }
        
        // Stop media streams
        if (videoStream.value) {
            videoStream.value.getTracks().forEach(track => track.stop())
        }
        
        if (audioStream.value) {
            audioStream.value.getTracks().forEach(track => track.stop())
        }
        
        // Remove event listeners
        removeEventListeners()
    }

    /**
     * Load face-api.js models
     */
    const loadFaceApiModels = async () => {
        const modelUrl = '/models'
        
        await Promise.all([
            faceapi.nets.tinyFaceDetector.loadFromUri(modelUrl),
            faceapi.nets.faceLandmark68Net.loadFromUri(modelUrl),
            faceapi.nets.faceRecognitionNet.loadFromUri(modelUrl)
        ])
    }

    /**
     * Initialize webcam
     */
    const initializeWebcam = async () => {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    width: { ideal: 640 },
                    height: { ideal: 480 },
                    facingMode: 'user'
                }
            })
            
            videoStream.value = stream
            
        } catch (error) {
            throw new Error(`Webcam access denied: ${error.message}`)
        }
    }

    /**
     * Initialize microphone
     */
    const initializeMicrophone = async () => {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true
                }
            })
            
            audioStream.value = stream
            
            // Set up audio analysis
            audioContext.value = new (window.AudioContext || window.webkitAudioContext)()
            const source = audioContext.value.createMediaStreamSource(stream)
            audioAnalyser.value = audioContext.value.createAnalyser()
            audioAnalyser.value.fftSize = 256
            
            source.connect(audioAnalyser.value)
            
            const bufferLength = audioAnalyser.value.frequencyBinCount
            audioDataArray.value = new Uint8Array(bufferLength)
            
        } catch (error) {
            throw new Error(`Microphone access denied: ${error.message}`)
        }
    }

    /**
     * Initialize screen recording
     */
    const initializeScreenRecording = async () => {
        try {
            const stream = await navigator.mediaDevices.getDisplayMedia({
                video: true,
                audio: true
            })
            
            screenRecorder.value = new RecordRTC(stream, {
                type: 'video',
                mimeType: 'video/webm',
                timeSlice: 30000, // 30 second chunks
                ondataavailable: (blob) => {
                    // Upload screen recording chunk
                    uploadScreenRecording(blob)
                }
            })
            
        } catch (error) {
            throw new Error(`Screen recording access denied: ${error.message}`)
        }
    }

    /**
     * Start webcam capture at intervals
     */
    const startWebcamCapture = () => {
        const interval = proctorConfig.capture_interval_seconds * 1000
        
        webcamCaptureInterval.value = setInterval(async () => {
            await captureWebcamImage()
        }, interval)
        
        // Capture immediately
        captureWebcamImage()
    }

    /**
     * Capture webcam image
     */
    const captureWebcamImage = async () => {
        if (!videoStream.value) return
        
        try {
            const video = document.createElement('video')
            video.srcObject = videoStream.value
            video.play()
            
            await new Promise(resolve => {
                video.onloadedmetadata = resolve
            })
            
            const canvas = document.createElement('canvas')
            canvas.width = video.videoWidth
            canvas.height = video.videoHeight
            
            const ctx = canvas.getContext('2d')
            ctx.drawImage(video, 0, 0)
            
            // Convert to blob
            canvas.toBlob(async (blob) => {
                let faceData = null
                
                // Detect faces if enabled
                if (proctorConfig.face_detection) {
                    faceData = await detectFaces(canvas)
                }
                
                // Upload capture
                await uploadWebcamCapture(blob, faceData)
                eventCounts.webcamCaptures++
                
            }, 'image/jpeg', 0.8)
            
        } catch (error) {
            console.error('Failed to capture webcam image:', error)
        }
    }

    /**
     * Detect faces in image
     */
    const detectFaces = async (canvas) => {
        try {
            const detections = await faceapi
                .detectAllFaces(canvas, new faceapi.TinyFaceDetectorOptions())
                .withFaceLandmarks()
                .withFaceDescriptors()
            
            lastFaceDetection.value = Date.now()
            
            if (detections.length === 0) {
                consecutiveNoFaceCount.value++
                
                if (consecutiveNoFaceCount.value >= proctorConfig.max_face_detection_failures) {
                    await logFaceDetectionFailure('No face detected for extended period')
                    eventCounts.faceDetectionFailures++
                }
            } else {
                consecutiveNoFaceCount.value = 0
                
                if (detections.length > 1) {
                    await logSuspiciousActivity('Multiple faces detected', {
                        face_count: detections.length
                    })
                }
            }
            
            return detections.map(detection => ({
                confidence: detection.detection.score,
                box: detection.detection.box
            }))
            
        } catch (error) {
            console.error('Face detection failed:', error)
            await logFaceDetectionFailure(`Face detection error: ${error.message}`)
            return null
        }
    }

    // API calls for logging events
    const uploadWebcamCapture = async (blob, faceData) => {
        const formData = new FormData()
        formData.append('submission_uuid', submissionUuid)
        formData.append('image', blob, 'webcam_capture.jpg')
        
        if (faceData) {
            formData.append('face_data', JSON.stringify(faceData))
        }
        
        try {
            await store.dispatch('api/post', {
                url: `online-exams/${examUuid}/proctoring/webcam-capture`,
                data: formData,
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            })
        } catch (error) {
            console.error('Failed to upload webcam capture:', error)
        }
    }

    const logFaceDetectionFailure = async (reason) => {
        try {
            await store.dispatch('api/post', {
                url: `online-exams/${examUuid}/proctoring/face-detection-failure`,
                data: {
                    submission_uuid: submissionUuid,
                    reason
                }
            })
        } catch (error) {
            console.error('Failed to log face detection failure:', error)
        }
    }

    const logSuspiciousActivity = async (activity, details = {}) => {
        // This would be implemented as a separate endpoint
        console.warn('Suspicious activity detected:', activity, details)
    }

    return {
        // State
        isInitialized,
        isActive,
        errors,
        eventCounts,
        
        // Methods
        initialize,
        startMonitoring,
        stopMonitoring,
        
        // Media streams (for external access if needed)
        videoStream,
        audioStream
    }
}
