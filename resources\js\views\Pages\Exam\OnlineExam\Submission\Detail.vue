<template>
    <BaseModal :show="visibility" @close="closeModal" v-if="submission.uuid">
        <template #title>
            {{ submission.studentName }}
            <TextMuted
                >{{ submission.admissionNumber }} -
                {{
                    submission.courseName + " " + submission.batchName
                }}</TextMuted
            >
        </template>

        <dl class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
            <BaseDataView
                :label="$trans('exam.online_exam.submission.props.started_at')"
            >
                {{ submission.startedAt.formatted }}
            </BaseDataView>
            <BaseDataView
                :label="
                    $trans('exam.online_exam.submission.props.submitted_at')
                "
            >
                {{ submission.submittedAt.formatted }}
            </BaseDataView>
            <BaseDataView
                :label="
                    $trans('exam.online_exam.submission.props.obtained_mark')
                "
            >
                {{ submission.obtainedMark }} / {{ submission.maxMark }}
            </BaseDataView>
        </dl>

        <!-- Tab Navigation -->
        <div class="mt-4">
            <div class="border-b border-gray-200 dark:border-gray-700">
                <nav class="-mb-px flex space-x-8">
                    <button
                        @click="activeTab = 'answers'"
                        :class="[
                            'py-2 px-1 border-b-2 font-medium text-sm',
                            activeTab === 'answers'
                                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                        ]"
                    >
                        {{ $trans("exam.online_exam.question.questions") || "Questions & Answers" }}
                    </button>
                    <button
                        v-if="submission.exam?.enableProctoring"
                        @click="activeTab = 'proctoring'"
                        :class="[
                            'py-2 px-1 border-b-2 font-medium text-sm',
                            activeTab === 'proctoring'
                                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                        ]"
                    >
                        {{ $trans("exam.proctoring.review.tab_title") || "Proctoring Review" }}
                        <span v-if="proctoringSummary?.critical_events > 0" class="ml-1 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            {{ proctoringSummary.critical_events }}
                        </span>
                    </button>
                </nav>
            </div>
        </div>

        <!-- Tab Content -->
        <div class="mt-6">
            <!-- Questions & Answers Tab -->
            <div v-show="activeTab === 'answers'">
                <div class="grid grid-cols-1 gap-6">
                    <div
                        class="col-span-3 sm:col-span-1"
                        v-for="(question, index) in submission.questions"
                        :key="question.uuid"
                    >
                        <div class="mb-4" v-if="question.header">
                            <div
                                class="text-lg font-semibold dark:text-gray-300"
                                v-html="question.header"
                            ></div>
                        </div>

                        <div class="dark:text-gray-400 flex justify-between">
                            <div class="flex items-center">
                                <span
                                    class="mr-2 text-lg text-gray-600 dark:text-gray-300"
                                    >{{ index + 1 }}.</span
                                >
                                <div v-html="question.title"></div>
                            </div>
                            <div class="flex gap-2">
                                <span
                                    class="font-semibold"
                                    :class="{
                                        'text-success': question.obtainedMark > 0,
                                        'text-danger': question.obtainedMark < 0,
                                    }"
                                    >{{ question.obtainedMark }} /
                                </span>
                                <span class="font-semibold">{{
                                    question.mark
                                }}</span>
                            </div>
                        </div>

                        <div
                            class="dark:text-gray-300 font-semibold text-sm italic"
                        >
                            <span v-if="question.type.value == 'mcq'">
                                <i
                                    class="text-success far fa-check-circle fa-lg"
                                    v-if="question.obtainedMark > 0"
                                ></i>
                                <i
                                    class="text-danger far fa-times-circle fa-lg"
                                    v-else
                                ></i>
                            </span>
                            <!-- Display multi-line answers with proper formatting -->
                            <pre v-if="question.type.value === 'multi_line_question' || question.type.value === 'single_line_question'" class="whitespace-pre-wrap break-words text-sm font-normal">{{ question.answer }}</pre>
                            <span class="ml-1" v-else>{{ question.answer }}</span>
                            <span
                                class="ml-1 text-success"
                                v-if="
                                    question.type.value == 'mcq' &&
                                    question.obtainedMark <= 0
                                "
                                >{{ question.correctAnswer }}</span
                            >
                        </div>
                    </div>
                </div>
            </div>

            <!-- Proctoring Review Tab -->
            <div v-show="activeTab === 'proctoring'" v-if="submission.exam?.enableProctoring">
                <ProctorReviewDashboard
                    :exam-uuid="submission.exam.uuid"
                    :submission-uuid="submission.uuid"
                />
            </div>
        </div>
    </BaseModal>
</template>

<script>
export default {
    name: "ExamOnlineExamSubmissionDetail",
}
</script>

<script setup>
import { onMounted, ref, reactive, watch } from "vue"
import { useRoute } from "vue-router"
import { useStore } from "vuex"
import ProctorReviewDashboard from "@core/components/Exam/ProctorReviewDashboard.vue"

const route = useRoute()
const store = useStore()

const emit = defineEmits(["close", "refresh"])

const props = defineProps({
    visibility: {
        type: Boolean,
        default: false,
    },
    submission: {
        type: Object,
        default: () => {},
    },
})

const initUrl = "exam/onlineExam/submission/"

const isLoading = ref(false)
const activeTab = ref('answers')
const proctoringSummary = ref(null)

const submission = reactive({})

const getQuestions = async () => {
    isLoading.value = true
    await store
        .dispatch(initUrl + "getQuestions", {
            uuid: route.params.uuid,
            moduleUuid: props.submission.uuid,
        })
        .then((response) => {
            const responseData = { ...response }
            if (responseData.exam && responseData.exam.hasOwnProperty('enableProctoring')) {
                // Ensure enableProctoring is a proper boolean
                responseData.exam.enableProctoring = Boolean(Number(responseData.exam.enableProctoring))
            }
            Object.assign(submission, responseData)

            // Load proctoring summary if proctoring is enabled
            if (submission.exam?.enableProctoring) {
                loadProctoringSummary()
            }

            isLoading.value = false
        })
        .catch((e) => {
            isLoading.value = false
        })
}

const loadProctoringSummary = async () => {
    try {
        const response = await store.dispatch('api/get', {
            url: `online-exams/${submission.exam.uuid}/proctoring/submissions/${submission.uuid}/summary`
        })
        proctoringSummary.value = response.summary
    } catch (error) {
        console.error('Failed to load proctoring summary:', error)
    }
}

const closeModal = () => {
    emit("close")
}

onMounted(() => {
    // getQuestions()
})

watch(
    props.submission,
    (value) => {
        getQuestions()
    },
    { deep: true, immediate: true }
)
</script>
