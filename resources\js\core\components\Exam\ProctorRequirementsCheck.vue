<template>
    <BaseModal :show="visibility" @close="$emit('close')" size="lg" :closable="false">
        <template #title>
            <div class="flex items-center space-x-2">
                <i class="fas fa-shield-alt text-blue-500"></i>
                <span>{{ $trans("exam.proctoring.requirements.title") || "Proctoring Requirements" }}</span>
            </div>
        </template>

        <div class="space-y-6">
            <!-- Introduction -->
            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
                    {{ $trans("exam.proctoring.requirements.intro_title") || "This exam requires proctoring" }}
                </h3>
                <p class="text-blue-800 dark:text-blue-200 text-sm">
                    {{ $trans("exam.proctoring.requirements.intro_text") || "To maintain academic integrity, this exam will monitor your activity. Please ensure you meet all requirements below before proceeding." }}
                </p>
            </div>

            <!-- Custom Instructions -->
            <div v-if="exam.proctorConfig.customInstructions" class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <h4 class="font-medium text-yellow-900 dark:text-yellow-100 mb-2">
                    {{ $trans("exam.proctoring.requirements.special_instructions") || "Special Instructions" }}
                </h4>
                <p class="text-yellow-800 dark:text-yellow-200 text-sm">
                    {{ exam.proctorConfig.customInstructions }}
                </p>
            </div>

            <!-- Requirements Checklist -->
            <div class="space-y-4">
                <h4 class="text-lg font-semibold text-gray-900 dark:text-white">
                    {{ $trans("exam.proctoring.requirements.checklist_title") || "Requirements Checklist" }}
                </h4>

                <!-- Webcam Requirement -->
                <div v-if="exam.proctorConfig.webcamMonitoring" class="flex items-start space-x-3 p-4 border rounded-lg" :class="getRequirementClass('webcam')">
                    <div class="flex-shrink-0 mt-1">
                        <i class="fas fa-video text-lg" :class="getIconClass('webcam')"></i>
                    </div>
                    <div class="flex-1">
                        <h5 class="font-medium text-gray-900 dark:text-white">
                            {{ $trans("exam.proctoring.requirements.webcam_title") || "Webcam Access" }}
                        </h5>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            {{ $trans("exam.proctoring.requirements.webcam_description") || "Your webcam will capture periodic images during the exam" }}
                        </p>
                        <div class="mt-2">
                            <BaseButton
                                size="sm"
                                :design="requirements.webcam ? 'success' : 'primary'"
                                @click="checkWebcam"
                                :disabled="isChecking.webcam"
                            >
                                <i v-if="isChecking.webcam" class="fas fa-spinner fa-spin mr-1"></i>
                                <i v-else-if="requirements.webcam" class="fas fa-check mr-1"></i>
                                <i v-else class="fas fa-camera mr-1"></i>
                                {{ getButtonText('webcam') }}
                            </BaseButton>
                        </div>
                    </div>
                </div>

                <!-- Microphone Requirement -->
                <div v-if="exam.proctorConfig.microphoneMonitoring" class="flex items-start space-x-3 p-4 border rounded-lg" :class="getRequirementClass('microphone')">
                    <div class="flex-shrink-0 mt-1">
                        <i class="fas fa-microphone text-lg" :class="getIconClass('microphone')"></i>
                    </div>
                    <div class="flex-1">
                        <h5 class="font-medium text-gray-900 dark:text-white">
                            {{ $trans("exam.proctoring.requirements.microphone_title") || "Microphone Access" }}
                        </h5>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            {{ $trans("exam.proctoring.requirements.microphone_description") || "Your microphone will monitor audio levels for suspicious activity" }}
                        </p>
                        <div class="mt-2">
                            <BaseButton
                                size="sm"
                                :design="requirements.microphone ? 'success' : 'primary'"
                                @click="checkMicrophone"
                                :disabled="isChecking.microphone"
                            >
                                <i v-if="isChecking.microphone" class="fas fa-spinner fa-spin mr-1"></i>
                                <i v-else-if="requirements.microphone" class="fas fa-check mr-1"></i>
                                <i v-else class="fas fa-microphone mr-1"></i>
                                {{ getButtonText('microphone') }}
                            </BaseButton>
                        </div>
                    </div>
                </div>

                <!-- Screen Recording Requirement -->
                <div v-if="exam.proctorConfig.screenRecording" class="flex items-start space-x-3 p-4 border rounded-lg" :class="getRequirementClass('screen')">
                    <div class="flex-shrink-0 mt-1">
                        <i class="fas fa-desktop text-lg" :class="getIconClass('screen')"></i>
                    </div>
                    <div class="flex-1">
                        <h5 class="font-medium text-gray-900 dark:text-white">
                            {{ $trans("exam.proctoring.requirements.screen_title") || "Screen Recording" }}
                        </h5>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            {{ $trans("exam.proctoring.requirements.screen_description") || "Your screen activity will be recorded during the exam" }}
                        </p>
                        <div class="mt-2">
                            <BaseButton
                                size="sm"
                                :design="requirements.screen ? 'success' : 'primary'"
                                @click="checkScreenRecording"
                                :disabled="isChecking.screen"
                            >
                                <i v-if="isChecking.screen" class="fas fa-spinner fa-spin mr-1"></i>
                                <i v-else-if="requirements.screen" class="fas fa-check mr-1"></i>
                                <i v-else class="fas fa-desktop mr-1"></i>
                                {{ getButtonText('screen') }}
                            </BaseButton>
                        </div>
                    </div>
                </div>

                <!-- Browser Requirements -->
                <div class="flex items-start space-x-3 p-4 border rounded-lg" :class="getRequirementClass('browser')">
                    <div class="flex-shrink-0 mt-1">
                        <i class="fas fa-globe text-lg" :class="getIconClass('browser')"></i>
                    </div>
                    <div class="flex-1">
                        <h5 class="font-medium text-gray-900 dark:text-white">
                            {{ $trans("exam.proctoring.requirements.browser_title") || "Browser Compatibility" }}
                        </h5>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            {{ $trans("exam.proctoring.requirements.browser_description") || "Your browser supports all required proctoring features" }}
                        </p>
                        <div class="mt-2 text-sm">
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium" :class="getBrowserStatusClass()">
                                <i class="fas fa-check mr-1" v-if="requirements.browser"></i>
                                <i class="fas fa-times mr-1" v-else></i>
                                {{ getBrowserStatusText() }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Privacy Notice -->
            <div class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                <h4 class="font-medium text-gray-900 dark:text-white mb-2">
                    {{ $trans("exam.proctoring.requirements.privacy_title") || "Privacy Notice" }}
                </h4>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                    {{ $trans("exam.proctoring.requirements.privacy_text") || "All proctoring data is collected solely for academic integrity purposes and will be handled according to our privacy policy. Data will be automatically deleted after the retention period." }}
                </p>
            </div>

            <!-- Error Messages -->
            <div v-if="errors.length > 0" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <h4 class="font-medium text-red-900 dark:text-red-100 mb-2">
                    {{ $trans("exam.proctoring.requirements.errors_title") || "Requirements Not Met" }}
                </h4>
                <ul class="text-sm text-red-800 dark:text-red-200 space-y-1">
                    <li v-for="error in errors" :key="error">• {{ error }}</li>
                </ul>
            </div>
        </div>

        <div class="flex justify-between p-4">
            <BaseButton design="secondary" @click="$emit('close')">
                {{ $trans("general.cancel") || "Cancel" }}
            </BaseButton>
            <BaseButton 
                design="primary" 
                @click="proceedToExam"
                :disabled="!allRequirementsMet || isValidating"
            >
                <i v-if="isValidating" class="fas fa-spinner fa-spin mr-2"></i>
                {{ $trans("exam.proctoring.requirements.proceed") || "Start Proctored Exam" }}
            </BaseButton>
        </div>
    </BaseModal>
</template>

<script>
export default {
    name: "ProctorRequirementsCheck",
}
</script>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import * as Api from '@core/apis'

const props = defineProps({
    visibility: {
        type: Boolean,
        default: false
    },
    exam: {
        type: Object,
        required: true
    }
})

const emit = defineEmits(['close', 'requirements-met'])

// State
const requirements = reactive({
    webcam: false,
    microphone: false,
    screen: false,
    browser: false
})

const isChecking = reactive({
    webcam: false,
    microphone: false,
    screen: false
})

const isValidating = ref(false)
const errors = ref([])

// Computed
const allRequirementsMet = computed(() => {
    const required = []

    if (props.exam.proctorConfig.webcamMonitoring) required.push('webcam')
    if (props.exam.proctorConfig.microphoneMonitoring) required.push('microphone')
    if (props.exam.proctorConfig.screenRecording) required.push('screen')

    required.push('browser') // Always required

    return required.every(req => requirements[req])
})

// Methods
const checkWebcam = async () => {
    isChecking.webcam = true
    try {
        const stream = await navigator.mediaDevices.getUserMedia({ video: true })
        requirements.webcam = true
        stream.getTracks().forEach(track => track.stop())
    } catch (error) {
        requirements.webcam = false
        console.error('Webcam access denied:', error)
    } finally {
        isChecking.webcam = false
    }
}

const checkMicrophone = async () => {
    isChecking.microphone = true
    try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
        requirements.microphone = true
        stream.getTracks().forEach(track => track.stop())
    } catch (error) {
        requirements.microphone = false
        console.error('Microphone access denied:', error)
    } finally {
        isChecking.microphone = false
    }
}

const checkScreenRecording = async () => {
    isChecking.screen = true
    try {
        const stream = await navigator.mediaDevices.getDisplayMedia({ video: true })
        requirements.screen = true
        stream.getTracks().forEach(track => track.stop())
    } catch (error) {
        requirements.screen = false
        console.error('Screen recording access denied:', error)
    } finally {
        isChecking.screen = false
    }
}

const checkBrowserCompatibility = () => {
    const hasMediaDevices = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
    const hasGetDisplayMedia = !!(navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia)
    const hasFullscreenAPI = !!(document.fullscreenEnabled || document.webkitFullscreenEnabled)
    
    requirements.browser = hasMediaDevices && hasGetDisplayMedia && hasFullscreenAPI
}

const proceedToExam = async () => {
    isValidating.value = true
    errors.value = []

    try {
        await Api.custom({
            url: `app/online-exams/${props.exam.uuid}/proctoring/validate-requirements`,
            method: 'POST',
            data: {
                webcamAvailable: requirements.webcam,
                microphoneAvailable: requirements.microphone,
                screenRecordingAvailable: requirements.screen,
                browserCompatible: requirements.browser
            }
        })

        emit('requirements-met')
    } catch (error) {
        if (error.response?.data?.errors) {
            errors.value = Object.values(error.response.data.errors).flat()
        } else {
            errors.value = ['Failed to validate proctoring requirements. Please try again.']
        }
    } finally {
        isValidating.value = false
    }
}

const getRequirementClass = (type) => {
    if (requirements[type]) {
        return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
    }
    return 'border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800'
}

const getIconClass = (type) => {
    if (requirements[type]) {
        return 'text-green-500'
    }
    return 'text-gray-400'
}

const getButtonText = (type) => {
    if (isChecking[type]) return 'Checking...'
    if (requirements[type]) return 'Granted'
    return 'Grant Access'
}

const getBrowserStatusClass = () => {
    if (requirements.browser) {
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
    }
    return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
}

const getBrowserStatusText = () => {
    if (requirements.browser) {
        return 'Compatible'
    }
    return 'Incompatible'
}

// Lifecycle
onMounted(() => {
    checkBrowserCompatibility()
})
</script>
