<template>
    <div class="space-y-4">
        <div class="flex items-center justify-between">
            <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                {{ $trans("exam.proctoring.config.quick_setup") || "Quick Setup" }}
            </h4>
            <BaseButton
                size="xs"
                design="secondary"
                @click="showCustomizer = !showCustomizer"
            >
                <i class="fas fa-cog mr-1"></i>
                {{ showCustomizer ? ($trans("general.hide") || "Hide") : ($trans("general.customize") || "Customize") }}
            </BaseButton>
        </div>

        <!-- Preset Selection -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div
                v-for="preset in presets"
                :key="preset.value"
                class="relative cursor-pointer rounded-lg border p-4 transition-all duration-200 hover:shadow-md"
                :class="[
                    selectedPreset === preset.value
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                ]"
                @click="selectPreset(preset.value)"
            >
                <div class="flex items-center space-x-2 mb-2">
                    <i :class="[preset.icon, preset.iconColor, 'w-5 h-5']"></i>
                    <h5 class="font-medium text-gray-900 dark:text-white">
                        {{ preset.label }}
                    </h5>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    {{ preset.description }}
                </p>
                
                <!-- Feature indicators -->
                <div class="flex flex-wrap gap-1">
                    <span
                        v-for="feature in preset.features"
                        :key="feature"
                        class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"
                        :class="getFeatureClass(feature)"
                    >
                        {{ getFeatureLabel(feature) }}
                    </span>
                </div>

                <!-- Selection indicator -->
                <div
                    v-if="selectedPreset === preset.value"
                    class="absolute top-2 right-2 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center"
                >
                    <i class="fas fa-check text-white text-xs"></i>
                </div>
            </div>
        </div>

        <!-- Custom Configuration Panel -->
        <div v-if="showCustomizer" class="mt-6">
            <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-4">
                    {{ $trans("exam.proctoring.config.custom_configuration") || "Custom Configuration" }}
                </h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <!-- Monitoring Features -->
                    <div class="space-y-3">
                        <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300">
                            {{ $trans("exam.proctoring.config.monitoring") || "Monitoring" }}
                        </h5>
                        <div class="space-y-2">
                            <BaseCheckbox
                                v-model="customConfig.webcamMonitoring"
                                :label="$trans('exam.proctoring.config.webcam') || 'Webcam'"
                            />
                            <BaseCheckbox
                                v-model="customConfig.microphoneMonitoring"
                                :label="$trans('exam.proctoring.config.microphone') || 'Microphone'"
                            />
                            <BaseCheckbox
                                v-model="customConfig.faceDetection"
                                :label="$trans('exam.proctoring.config.face_detection') || 'Face Detection'"
                            />
                            <BaseCheckbox
                                v-model="customConfig.screenRecording"
                                :label="$trans('exam.proctoring.config.screen_recording') || 'Screen Recording'"
                            />
                        </div>
                    </div>

                    <!-- Security Features -->
                    <div class="space-y-3">
                        <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300">
                            {{ $trans("exam.proctoring.config.security") || "Security" }}
                        </h5>
                        <div class="space-y-2">
                            <BaseCheckbox
                                v-model="customConfig.fullscreenEnforcement"
                                :label="$trans('exam.proctoring.config.fullscreen') || 'Fullscreen'"
                            />
                            <BaseCheckbox
                                v-model="customConfig.copyPasteBlocking"
                                :label="$trans('exam.proctoring.config.copy_paste_blocking') || 'Block Copy/Paste'"
                            />
                            <BaseCheckbox
                                v-model="customConfig.allowTabSwitching"
                                :label="$trans('exam.proctoring.config.allow_tab_switching') || 'Allow Tab Switch'"
                            />
                            <BaseCheckbox
                                v-model="customConfig.autoSubmitOnViolations"
                                :label="$trans('exam.proctoring.config.auto_submit') || 'Auto Submit'"
                            />
                        </div>
                    </div>

                    <!-- Advanced Settings -->
                    <div class="space-y-3">
                        <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300">
                            {{ $trans("exam.proctoring.config.advanced") || "Advanced" }}
                        </h5>
                        <div class="space-y-3">
                            <div>
                                <label class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                    {{ $trans("exam.proctoring.config.capture_interval") || "Capture Interval (s)" }}
                                </label>
                                <BaseInput
                                    type="number"
                                    v-model="customConfig.captureIntervalSeconds"
                                    min="10"
                                    max="300"
                                    class="text-sm"
                                />
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                    {{ $trans("exam.proctoring.config.audio_threshold") || "Audio Threshold (dB)" }}
                                </label>
                                <BaseInput
                                    type="number"
                                    v-model="customConfig.audioThresholdDb"
                                    min="-80"
                                    max="0"
                                    class="text-sm"
                                />
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                    {{ $trans("exam.proctoring.config.max_face_failures") || "Max Face Failures" }}
                                </label>
                                <BaseInput
                                    type="number"
                                    v-model="customConfig.maxFaceDetectionFailures"
                                    min="1"
                                    max="20"
                                    class="text-sm"
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4 flex justify-end">
                    <BaseButton
                        size="sm"
                        design="primary"
                        @click="applyCustomConfig"
                    >
                        {{ $trans("general.apply") || "Apply Configuration" }}
                    </BaseButton>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "ProctorPresetSelector",
}
</script>

<script setup>
import { ref, reactive } from 'vue'

const props = defineProps({
    modelValue: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['update:modelValue', 'preset-selected'])

// State
const selectedPreset = ref('medium')
const showCustomizer = ref(false)

// Custom configuration for advanced users
const customConfig = reactive({
    webcamMonitoring: true,
    microphoneMonitoring: true,
    screenRecording: true,
    fullscreenEnforcement: true,
    copyPasteBlocking: true,
    faceDetection: true,
    captureIntervalSeconds: 30,
    audioThresholdDb: -40,
    maxFaceDetectionFailures: 5,
    allowTabSwitching: false,
    autoSubmitOnViolations: false
})

// Preset configurations
const presets = [
    {
        value: 'disabled',
        label: 'Disabled',
        description: 'No proctoring features enabled',
        icon: 'fas fa-ban',
        iconColor: 'text-gray-500',
        features: [],
        config: { enableProctoring: false }
    },
    {
        value: 'low',
        label: 'Low Security',
        description: 'Basic monitoring for low-stakes assessments',
        icon: 'fas fa-lock-open',
        iconColor: 'text-green-500',
        features: ['webcam', 'copy_paste'],
        config: {
            enableProctoring: true,
            webcamMonitoring: true,
            microphoneMonitoring: false,
            screenRecording: false,
            fullscreenEnforcement: false,
            copyPasteBlocking: true,
            faceDetection: false,
            captureIntervalSeconds: 60,
            audioThresholdDb: -30,
            maxFaceDetectionFailures: 10,
            allowTabSwitching: true,
            autoSubmitOnViolations: false
        }
    },
    {
        value: 'medium',
        label: 'Medium Security',
        description: 'Balanced monitoring for standard exams',
        icon: 'fas fa-lock',
        iconColor: 'text-yellow-500',
        features: ['webcam', 'microphone', 'face_detection', 'fullscreen'],
        config: {
            enableProctoring: true,
            webcamMonitoring: true,
            microphoneMonitoring: true,
            screenRecording: true,
            fullscreenEnforcement: true,
            copyPasteBlocking: true,
            faceDetection: true,
            captureIntervalSeconds: 30,
            audioThresholdDb: -40,
            maxFaceDetectionFailures: 5,
            allowTabSwitching: false,
            autoSubmitOnViolations: false
        }
    },
    {
        value: 'high',
        label: 'High Security',
        description: 'Comprehensive monitoring for important exams',
        icon: 'fas fa-shield-alt',
        iconColor: 'text-red-500',
        features: ['webcam', 'microphone', 'face_detection', 'screen_recording', 'auto_submit'],
        config: {
            enableProctoring: true,
            webcamMonitoring: true,
            microphoneMonitoring: true,
            screenRecording: true,
            fullscreenEnforcement: true,
            copyPasteBlocking: true,
            faceDetection: true,
            captureIntervalSeconds: 20,
            audioThresholdDb: -50,
            maxFaceDetectionFailures: 3,
            allowTabSwitching: false,
            autoSubmitOnViolations: true
        }
    }
]

// Methods
const selectPreset = (presetValue) => {
    selectedPreset.value = presetValue
    const preset = presets.find(p => p.value === presetValue)
    
    if (preset) {
        emit('update:modelValue', preset.config)
        emit('preset-selected', preset)
    }
}

const applyCustomConfig = () => {
    const config = {
        enableProctoring: true,
        ...customConfig
    }

    selectedPreset.value = 'custom'
    emit('update:modelValue', config)
    emit('preset-selected', { value: 'custom', label: 'Custom', config })
}

const getFeatureClass = (feature) => {
    const classes = {
        webcam: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
        microphone: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
        face_detection: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
        screen_recording: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200',
        fullscreen: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
        copy_paste: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
        auto_submit: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
    }
    
    return classes[feature] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
}

const getFeatureLabel = (feature) => {
    const labels = {
        webcam: 'Webcam',
        microphone: 'Audio',
        face_detection: 'Face AI',
        screen_recording: 'Screen',
        fullscreen: 'Fullscreen',
        copy_paste: 'Copy Block',
        auto_submit: 'Auto Submit'
    }
    
    return labels[feature] || feature
}

// Initialize with medium preset
selectPreset('medium')
</script>
