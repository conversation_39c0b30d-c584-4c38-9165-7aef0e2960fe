<?php

namespace App\Http\Middleware;

use App\Models\Exam\OnlineExam;
use App\Models\Exam\OnlineExamSubmission;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ProctoringSecurity
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Only apply to proctoring-related routes
        if (!$this->isProctorRoute($request)) {
            return $next($request);
        }

        // Log all proctoring requests for security audit
        $this->logProctorRequest($request);

        // Validate request integrity
        if (!$this->validateRequestIntegrity($request)) {
            return response()->json([
                'message' => 'Invalid request integrity'
            ], 422);
        }

        // Rate limiting for proctoring events
        if (!$this->checkRateLimit($request)) {
            return response()->json([
                'message' => 'Rate limit exceeded'
            ], 429);
        }

        return $next($request);
    }

    /**
     * Check if this is a proctoring-related route
     */
    private function isProctorRoute(Request $request): bool
    {
        return str_contains($request->route()->getName() ?? '', 'proctoring');
    }

    /**
     * Log proctoring requests for security audit
     */
    private function logProctorRequest(Request $request): void
    {
        $logData = [
            'user_id' => auth()->id(),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'route' => $request->route()->getName(),
            'method' => $request->method(),
            'timestamp' => now()->toISOString(),
        ];

        // Don't log sensitive data like images/audio
        $filteredInput = collect($request->all())
            ->except(['image', 'audio_file', 'content'])
            ->toArray();

        $logData['input'] = $filteredInput;

        Log::channel('proctoring')->info('Proctoring request', $logData);
    }

    /**
     * Validate request integrity
     */
    private function validateRequestIntegrity(Request $request): bool
    {
        // Check for required headers
        $requiredHeaders = ['User-Agent', 'Accept'];
        foreach ($requiredHeaders as $header) {
            if (!$request->hasHeader($header)) {
                return false;
            }
        }

        // Validate submission UUID format if present
        if ($request->has('submission_uuid')) {
            $uuid = $request->input('submission_uuid');
            if (!preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $uuid)) {
                return false;
            }
        }

        // Additional integrity checks can be added here
        return true;
    }

    /**
     * Check rate limiting for proctoring events
     */
    private function checkRateLimit(Request $request): bool
    {
        $userId = auth()->id();
        $route = $request->route()->getName();
        
        // Different rate limits for different event types
        $rateLimits = [
            'webcam-capture' => ['limit' => 120, 'window' => 60], // 2 per second max
            'audio-alert' => ['limit' => 60, 'window' => 60], // 1 per second max
            'tab-switch' => ['limit' => 30, 'window' => 60], // 30 per minute max
            'fullscreen-exit' => ['limit' => 10, 'window' => 60], // 10 per minute max
            'copy-paste-attempt' => ['limit' => 20, 'window' => 60], // 20 per minute max
            'face-detection-failure' => ['limit' => 30, 'window' => 60], // 30 per minute max
        ];

        foreach ($rateLimits as $eventType => $limits) {
            if (str_contains($route, $eventType)) {
                $cacheKey = "proctoring_rate_limit:{$userId}:{$eventType}";
                $currentCount = cache()->get($cacheKey, 0);
                
                if ($currentCount >= $limits['limit']) {
                    return false;
                }
                
                cache()->put($cacheKey, $currentCount + 1, $limits['window']);
                break;
            }
        }

        return true;
    }
}
