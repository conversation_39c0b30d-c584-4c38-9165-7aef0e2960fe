<template>
    <div class="space-y-4">
        <!-- Loading State -->
        <div v-if="isLoading" class="flex justify-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>

        <!-- Empty State -->
        <div v-else-if="events.length === 0" class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                {{ $trans("exam.proctoring.review.no_events") || "No proctoring events" }}
            </h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {{ $trans("exam.proctoring.review.no_events_description") || "This submission has no recorded proctoring events." }}
            </p>
        </div>

        <!-- Timeline -->
        <div v-else class="flow-root">
            <ul role="list" class="-mb-8">
                <li v-for="(event, eventIdx) in events" :key="event.uuid">
                    <div class="relative pb-8">
                        <!-- Connector Line -->
                        <span
                            v-if="eventIdx !== events.length - 1"
                            class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-gray-600"
                            aria-hidden="true"
                        ></span>
                        
                        <div class="relative flex space-x-3">
                            <!-- Event Icon -->
                            <div>
                                <span
                                    class="h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white dark:ring-gray-800"
                                    :class="getEventIconClass(event)"
                                >
                                    <component :is="getEventIcon(event)" class="w-4 h-4" />
                                </span>
                            </div>
                            
                            <!-- Event Content -->
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-900 dark:text-white">
                                            {{ getEventTitle(event) }}
                                        </p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ event.formatted_description || event.description }}
                                        </p>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                            :class="getSeverityClass(event.severity)"
                                        >
                                            {{ event.severity }}
                                        </span>
                                        <time class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ formatTime(event.detected_at) }}
                                        </time>
                                    </div>
                                </div>
                                
                                <!-- Event Details -->
                                <div v-if="event.data" class="mt-2 text-sm text-gray-600 dark:text-gray-300">
                                    <div v-if="event.data.face_count !== undefined" class="inline-block mr-4">
                                        <span class="font-medium">Faces:</span> {{ event.data.face_count }}
                                    </div>
                                    <div v-if="event.data.audio_level !== undefined" class="inline-block mr-4">
                                        <span class="font-medium">Audio Level:</span> {{ event.data.audio_level }}dB
                                    </div>
                                    <div v-if="event.data.tab_title" class="inline-block mr-4">
                                        <span class="font-medium">Tab:</span> {{ event.data.tab_title }}
                                    </div>
                                </div>
                                
                                <!-- Action Buttons -->
                                <div class="mt-3 flex space-x-2">
                                    <BaseButton
                                        v-if="event.has_media"
                                        size="xs"
                                        design="secondary"
                                        @click="$emit('view-media', { event, url: event.media_url })"
                                    >
                                        <i class="fas fa-eye mr-1"></i>
                                        {{ getMediaButtonText(event.event_type) }}
                                    </BaseButton>
                                    <BaseButton
                                        size="xs"
                                        design="secondary"
                                        @click="$emit('view-details', event)"
                                    >
                                        <i class="fas fa-info-circle mr-1"></i>
                                        {{ $trans("general.details") || "Details" }}
                                    </BaseButton>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</template>

<script>
export default {
    name: "ProctorEventTimeline",
}
</script>

<script setup>
import { computed } from 'vue'

defineEmits(['view-media', 'view-details'])

const props = defineProps({
    events: {
        type: Array,
        default: () => []
    },
    isLoading: {
        type: Boolean,
        default: false
    }
})

const getEventIcon = (event) => {
    const iconMap = {
        webcam_capture: () => ({
            template: `<svg fill="currentColor" viewBox="0 0 20 20"><path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"></path></svg>`
        }),
        audio_alert: () => ({
            template: `<svg fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clip-rule="evenodd"></path></svg>`
        }),
        tab_switch: () => ({
            template: `<svg fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12z" clip-rule="evenodd"></path></svg>`
        }),
        fullscreen_exit: () => ({
            template: `<svg fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4z" clip-rule="evenodd"></path></svg>`
        }),
        copy_paste_attempt: () => ({
            template: `<svg fill="currentColor" viewBox="0 0 20 20"><path d="M8 2a1 1 0 000 2h2a1 1 0 100-2H8z"></path><path d="M3 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6h-4.586l1.293-1.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L10.414 13H15v3a2 2 0 01-2 2H5a2 2 0 01-2-2V5z"></path></svg>`
        }),
        face_detection_failure: () => ({
            template: `<svg fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"></path></svg>`
        }),
        suspicious_activity: () => ({
            template: `<svg fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>`
        })
    }
    
    return iconMap[event.event_type] || iconMap.suspicious_activity
}

const getEventIconClass = (event) => {
    const baseClasses = 'text-white'
    
    switch (event.severity) {
        case 'critical':
            return `${baseClasses} bg-red-500`
        case 'warning':
            return `${baseClasses} bg-yellow-500`
        case 'info':
            return `${baseClasses} bg-blue-500`
        default:
            return `${baseClasses} bg-gray-500`
    }
}

const getSeverityClass = (severity) => {
    switch (severity) {
        case 'critical':
            return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
        case 'warning':
            return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
        case 'info':
            return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
        default:
            return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
}

const getEventTitle = (event) => {
    const titleMap = {
        webcam_capture: 'Webcam Capture',
        audio_alert: 'Audio Alert',
        tab_switch: 'Tab Switch Detected',
        fullscreen_exit: 'Fullscreen Exit',
        copy_paste_attempt: 'Copy/Paste Attempt',
        face_detection_failure: 'Face Detection Failure',
        suspicious_activity: 'Suspicious Activity',
        proctoring_started: 'Proctoring Started',
        proctoring_ended: 'Proctoring Ended'
    }
    
    return titleMap[event.event_type] || event.event_type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const getMediaButtonText = (eventType) => {
    switch (eventType) {
        case 'webcam_capture':
            return 'View Image'
        case 'audio_alert':
            return 'Play Audio'
        case 'screen_recording':
            return 'View Recording'
        default:
            return 'View Media'
    }
}

const formatTime = (timestamp) => {
    if (!timestamp) return ''
    
    const date = new Date(timestamp)
    return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    })
}
</script>
