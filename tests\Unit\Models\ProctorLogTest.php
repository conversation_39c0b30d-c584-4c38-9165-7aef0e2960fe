<?php

use App\Models\Exam\OnlineExam;
use App\Models\Exam\OnlineExamRecord;
use App\Models\Exam\ProctorLog;
use App\Models\Student\Student;
use App\Models\Team;
use App\Models\User;
use function Pest\Faker\fake;

beforeEach(function () {
    $this->team = Team::factory()->create();
    $this->user = User::factory()->create(['team_id' => $this->team->id]);
    $this->student = Student::factory()->create(['team_id' => $this->team->id]);
    $this->exam = OnlineExam::factory()->create(['team_id' => $this->team->id]);
    $this->examRecord = OnlineExamRecord::factory()->create([
        'online_exam_id' => $this->exam->id,
        'student_id' => $this->student->id,
    ]);
});

it('can create a proctor log', function () {
    $proctorLog = ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'event_type' => 'webcam_capture',
        'severity' => 'info',
        'description' => 'Webcam image captured',
        'data' => ['image_url' => 'path/to/image.jpg'],
        'ip_address' => '***********',
        'user_agent' => 'Mozilla/5.0...',
    ]);

    expect($proctorLog)->toBeInstanceOf(ProctorLog::class);
    expect($proctorLog->event_type)->toBe('webcam_capture');
    expect($proctorLog->severity)->toBe('info');
    expect($proctorLog->data)->toBeArray();
    expect($proctorLog->data['image_url'])->toBe('path/to/image.jpg');
});

it('belongs to an online exam', function () {
    $proctorLog = ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
    ]);

    expect($proctorLog->onlineExam)->toBeInstanceOf(OnlineExam::class);
    expect($proctorLog->onlineExam->id)->toBe($this->exam->id);
});

it('belongs to an online exam record', function () {
    $proctorLog = ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
    ]);

    expect($proctorLog->onlineExamRecord)->toBeInstanceOf(OnlineExamRecord::class);
    expect($proctorLog->onlineExamRecord->id)->toBe($this->examRecord->id);
});

it('belongs to a student', function () {
    $proctorLog = ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
    ]);

    expect($proctorLog->student)->toBeInstanceOf(Student::class);
    expect($proctorLog->student->id)->toBe($this->student->id);
});

it('can filter by event type', function () {
    ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'event_type' => 'webcam_capture',
    ]);

    ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'event_type' => 'audio_alert',
    ]);

    $webcamLogs = ProctorLog::filter(['event_type' => 'webcam_capture'])->get();
    $audioLogs = ProctorLog::filter(['event_type' => 'audio_alert'])->get();

    expect($webcamLogs)->toHaveCount(1);
    expect($audioLogs)->toHaveCount(1);
    expect($webcamLogs->first()->event_type)->toBe('webcam_capture');
    expect($audioLogs->first()->event_type)->toBe('audio_alert');
});

it('can filter by severity', function () {
    ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'severity' => 'critical',
    ]);

    ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'severity' => 'info',
    ]);

    $criticalLogs = ProctorLog::filter(['severity' => 'critical'])->get();
    $infoLogs = ProctorLog::filter(['severity' => 'info'])->get();

    expect($criticalLogs)->toHaveCount(1);
    expect($infoLogs)->toHaveCount(1);
    expect($criticalLogs->first()->severity)->toBe('critical');
    expect($infoLogs->first()->severity)->toBe('info');
});

it('can filter by date range', function () {
    $yesterday = now()->subDay();
    $today = now();

    ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'detected_at' => $yesterday,
    ]);

    ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'detected_at' => $today,
    ]);

    $todayLogs = ProctorLog::filter([
        'start_date' => $today->format('Y-m-d'),
        'end_date' => $today->format('Y-m-d'),
    ])->get();

    expect($todayLogs)->toHaveCount(1);
    expect($todayLogs->first()->detected_at->format('Y-m-d'))->toBe($today->format('Y-m-d'));
});

it('has correct fillable attributes', function () {
    $proctorLog = new ProctorLog();
    
    $expectedFillable = [
        'online_exam_id',
        'online_exam_record_id',
        'student_id',
        'event_type',
        'severity',
        'description',
        'data',
        'detected_at',
        'ip_address',
        'user_agent',
        'media_path',
        'meta',
    ];

    expect($proctorLog->getFillable())->toBe($expectedFillable);
});

it('casts data and meta as arrays', function () {
    $proctorLog = ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'data' => ['key' => 'value'],
        'meta' => ['meta_key' => 'meta_value'],
    ]);

    expect($proctorLog->data)->toBeArray();
    expect($proctorLog->meta)->toBeArray();
    expect($proctorLog->data['key'])->toBe('value');
    expect($proctorLog->meta['meta_key'])->toBe('meta_value');
});

it('can check if it has media', function () {
    $proctorLogWithMedia = ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'media_path' => 'path/to/media.jpg',
    ]);

    $proctorLogWithoutMedia = ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'media_path' => null,
    ]);

    expect($proctorLogWithMedia->hasMedia())->toBeTrue();
    expect($proctorLogWithoutMedia->hasMedia())->toBeFalse();
});

it('can get media url', function () {
    $proctorLog = ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'media_path' => 'proctoring/images/test.jpg',
    ]);

    $mediaUrl = $proctorLog->getMediaUrl();
    
    expect($mediaUrl)->toContain('proctoring/images/test.jpg');
    expect($mediaUrl)->toBeString();
});

it('scopes to exam', function () {
    $otherExam = OnlineExam::factory()->create(['team_id' => $this->team->id]);
    $otherExamRecord = OnlineExamRecord::factory()->create([
        'online_exam_id' => $otherExam->id,
        'student_id' => $this->student->id,
    ]);

    ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
    ]);

    ProctorLog::factory()->create([
        'online_exam_id' => $otherExam->id,
        'online_exam_record_id' => $otherExamRecord->id,
        'student_id' => $this->student->id,
    ]);

    $examLogs = ProctorLog::forExam($this->exam->id)->get();
    $otherExamLogs = ProctorLog::forExam($otherExam->id)->get();

    expect($examLogs)->toHaveCount(1);
    expect($otherExamLogs)->toHaveCount(1);
    expect($examLogs->first()->online_exam_id)->toBe($this->exam->id);
    expect($otherExamLogs->first()->online_exam_id)->toBe($otherExam->id);
});

it('scopes to student', function () {
    $otherStudent = Student::factory()->create(['team_id' => $this->team->id]);
    $otherExamRecord = OnlineExamRecord::factory()->create([
        'online_exam_id' => $this->exam->id,
        'student_id' => $otherStudent->id,
    ]);

    ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
    ]);

    ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $otherExamRecord->id,
        'student_id' => $otherStudent->id,
    ]);

    $studentLogs = ProctorLog::forStudent($this->student->id)->get();
    $otherStudentLogs = ProctorLog::forStudent($otherStudent->id)->get();

    expect($studentLogs)->toHaveCount(1);
    expect($otherStudentLogs)->toHaveCount(1);
    expect($studentLogs->first()->student_id)->toBe($this->student->id);
    expect($otherStudentLogs->first()->student_id)->toBe($otherStudent->id);
});
