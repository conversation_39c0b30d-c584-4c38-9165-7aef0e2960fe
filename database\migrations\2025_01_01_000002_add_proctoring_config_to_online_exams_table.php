<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('online_exams', function (Blueprint $table) {
            $table->boolean('enable_proctoring')->default(false)->after('is_flexible_timing');
            $table->json('proctor_config')->nullable()->after('enable_proctoring');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('online_exams', function (Blueprint $table) {
            $table->dropColumn(['enable_proctoring', 'proctor_config']);
        });
    }
};
