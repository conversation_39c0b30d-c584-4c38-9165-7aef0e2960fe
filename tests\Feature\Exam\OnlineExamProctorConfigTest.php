<?php

use App\Models\Exam\OnlineExam;
use App\Models\Team;
use App\Models\User;

beforeEach(function () {
    $this->team = Team::factory()->create();
    $this->user = User::factory()->create(['team_id' => $this->team->id]);
    $this->actingAs($this->user);
});

it('can create an online exam with proctoring configuration', function () {
    $examData = [
        'title' => 'Test Exam with Proctoring',
        'type' => 'exam',
        'isFlexibleTiming' => false,
        'date' => now()->addDays(7)->format('Y-m-d'),
        'startTime' => '09:00',
        'endDate' => now()->addDays(7)->format('Y-m-d'),
        'endTime' => '11:00',
        'durationMinutes' => 120,
        'batches' => [],
        'passPercentage' => 60,
        'hasNegativeMarking' => false,
        'instructions' => 'Test instructions',
        'description' => 'Test description',
        'enableProctoring' => true,
        'proctorConfig' => [
            'webcamMonitoring' => true,
            'microphoneMonitoring' => true,
            'screenRecording' => true,
            'fullscreenEnforcement' => true,
            'copyPasteBlocking' => true,
            'faceDetection' => true,
            'captureIntervalSeconds' => 30,
            'audioThresholdDb' => -40,
            'maxFaceDetectionFailures' => 5,
            'allowTabSwitching' => false,
            'autoSubmitOnViolations' => false,
            'customInstructions' => 'Please ensure good lighting.',
        ],
    ];

    $response = $this->postJson('/api/exam/online-exam', $examData);

    $response->assertStatus(201);
    $response->assertJsonStructure([
        'uuid',
        'title',
        'enable_proctoring',
        'proctor_config',
    ]);

    $exam = OnlineExam::where('uuid', $response->json('uuid'))->first();
    expect($exam->enable_proctoring)->toBeTrue();
    expect($exam->proctor_config)->toBeArray();
    expect($exam->proctor_config['webcam_monitoring'])->toBeTrue();
    expect($exam->proctor_config['capture_interval_seconds'])->toBe(30);
    expect($exam->proctor_config['custom_instructions'])->toBe('Please ensure good lighting.');
});

it('can create an online exam without proctoring', function () {
    $examData = [
        'title' => 'Test Exam without Proctoring',
        'type' => 'exam',
        'isFlexibleTiming' => false,
        'date' => now()->addDays(7)->format('Y-m-d'),
        'startTime' => '09:00',
        'endDate' => now()->addDays(7)->format('Y-m-d'),
        'endTime' => '11:00',
        'durationMinutes' => 120,
        'batches' => [],
        'passPercentage' => 60,
        'hasNegativeMarking' => false,
        'instructions' => 'Test instructions',
        'description' => 'Test description',
        'enableProctoring' => false,
        'proctorConfig' => [],
    ];

    $response = $this->postJson('/api/exam/online-exam', $examData);

    $response->assertStatus(201);
    
    $exam = OnlineExam::where('uuid', $response->json('uuid'))->first();
    expect($exam->enable_proctoring)->toBeFalse();
    expect($exam->proctor_config)->toBeArray();
    expect($exam->proctor_config)->toBeEmpty();
});

it('can update proctoring configuration for existing exam', function () {
    $exam = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => false,
        'proctor_config' => [],
    ]);

    $updateData = [
        'title' => $exam->title,
        'type' => $exam->type->value,
        'isFlexibleTiming' => $exam->is_flexible_timing,
        'date' => $exam->date->value,
        'startTime' => $exam->start_time->at,
        'endDate' => $exam->end_date->value,
        'endTime' => $exam->end_time->at,
        'durationMinutes' => $exam->duration_minutes,
        'batches' => [],
        'passPercentage' => $exam->pass_percentage->value,
        'hasNegativeMarking' => $exam->has_negative_marking,
        'instructions' => $exam->instructions,
        'description' => $exam->description,
        'enableProctoring' => true,
        'proctorConfig' => [
            'webcamMonitoring' => true,
            'microphoneMonitoring' => false,
            'screenRecording' => true,
            'fullscreenEnforcement' => true,
            'copyPasteBlocking' => true,
            'faceDetection' => false,
            'captureIntervalSeconds' => 60,
            'audioThresholdDb' => -30,
            'maxFaceDetectionFailures' => 10,
            'allowTabSwitching' => true,
            'autoSubmitOnViolations' => false,
            'customInstructions' => 'Updated instructions.',
        ],
    ];

    $response = $this->putJson("/api/exam/online-exam/{$exam->uuid}", $updateData);

    $response->assertStatus(200);
    
    $exam->refresh();
    expect($exam->enable_proctoring)->toBeTrue();
    expect($exam->proctor_config['webcam_monitoring'])->toBeTrue();
    expect($exam->proctor_config['microphone_monitoring'])->toBeFalse();
    expect($exam->proctor_config['capture_interval_seconds'])->toBe(60);
    expect($exam->proctor_config['custom_instructions'])->toBe('Updated instructions.');
});

it('validates proctoring configuration fields', function () {
    $examData = [
        'title' => 'Test Exam',
        'type' => 'exam',
        'isFlexibleTiming' => false,
        'date' => now()->addDays(7)->format('Y-m-d'),
        'startTime' => '09:00',
        'endDate' => now()->addDays(7)->format('Y-m-d'),
        'endTime' => '11:00',
        'durationMinutes' => 120,
        'batches' => [],
        'passPercentage' => 60,
        'hasNegativeMarking' => false,
        'instructions' => 'Test instructions',
        'description' => 'Test description',
        'enableProctoring' => true,
        'proctorConfig' => [
            'captureIntervalSeconds' => 5, // Invalid: too low
            'audioThresholdDb' => 10, // Invalid: too high
            'maxFaceDetectionFailures' => 0, // Invalid: too low
            'customInstructions' => str_repeat('a', 1001), // Invalid: too long
        ],
    ];

    $response = $this->postJson('/api/exam/online-exam', $examData);

    $response->assertStatus(422);
    $response->assertJsonValidationErrors([
        'proctorConfig.captureIntervalSeconds',
        'proctorConfig.audioThresholdDb',
        'proctorConfig.maxFaceDetectionFailures',
        'proctorConfig.customInstructions',
    ]);
});

it('can disable proctoring for existing exam', function () {
    $exam = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => [
            'webcam_monitoring' => true,
            'microphone_monitoring' => true,
        ],
    ]);

    $updateData = [
        'title' => $exam->title,
        'type' => $exam->type->value,
        'isFlexibleTiming' => $exam->is_flexible_timing,
        'date' => $exam->date->value,
        'startTime' => $exam->start_time->at,
        'endDate' => $exam->end_date->value,
        'endTime' => $exam->end_time->at,
        'durationMinutes' => $exam->duration_minutes,
        'batches' => [],
        'passPercentage' => $exam->pass_percentage->value,
        'hasNegativeMarking' => $exam->has_negative_marking,
        'instructions' => $exam->instructions,
        'description' => $exam->description,
        'enableProctoring' => false,
        'proctorConfig' => [],
    ];

    $response = $this->putJson("/api/exam/online-exam/{$exam->uuid}", $updateData);

    $response->assertStatus(200);
    
    $exam->refresh();
    expect($exam->enable_proctoring)->toBeFalse();
    expect($exam->proctor_config)->toBeArray();
    expect($exam->proctor_config)->toBeEmpty();
});

it('returns proctoring configuration when fetching exam details', function () {
    $exam = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => [
            'webcam_monitoring' => true,
            'microphone_monitoring' => true,
            'face_detection' => true,
            'capture_interval_seconds' => 45,
            'custom_instructions' => 'Test instructions',
        ],
    ]);

    $response = $this->getJson("/api/exam/online-exam/{$exam->uuid}");

    $response->assertStatus(200);
    $response->assertJsonStructure([
        'uuid',
        'title',
        'enable_proctoring',
        'proctor_config',
    ]);

    expect($response->json('enable_proctoring'))->toBeTrue();
    expect($response->json('proctor_config'))->toBeArray();
    expect($response->json('proctor_config.webcam_monitoring'))->toBeTrue();
    expect($response->json('proctor_config.capture_interval_seconds'))->toBe(45);
});

it('validates boolean fields in proctoring configuration', function () {
    $examData = [
        'title' => 'Test Exam',
        'type' => 'exam',
        'isFlexibleTiming' => false,
        'date' => now()->addDays(7)->format('Y-m-d'),
        'startTime' => '09:00',
        'endDate' => now()->addDays(7)->format('Y-m-d'),
        'endTime' => '11:00',
        'durationMinutes' => 120,
        'batches' => [],
        'passPercentage' => 60,
        'hasNegativeMarking' => false,
        'instructions' => 'Test instructions',
        'description' => 'Test description',
        'enableProctoring' => 'invalid_boolean', // Invalid boolean
        'proctorConfig' => [
            'webcamMonitoring' => 'not_boolean', // Invalid boolean
            'microphoneMonitoring' => 'also_not_boolean', // Invalid boolean
        ],
    ];

    $response = $this->postJson('/api/exam/online-exam', $examData);

    $response->assertStatus(422);
    $response->assertJsonValidationErrors([
        'enableProctoring',
        'proctorConfig.webcamMonitoring',
        'proctorConfig.microphoneMonitoring',
    ]);
});

it('can create exam with minimal proctoring configuration', function () {
    $examData = [
        'title' => 'Minimal Proctoring Exam',
        'type' => 'exam',
        'isFlexibleTiming' => false,
        'date' => now()->addDays(7)->format('Y-m-d'),
        'startTime' => '09:00',
        'endDate' => now()->addDays(7)->format('Y-m-d'),
        'endTime' => '11:00',
        'durationMinutes' => 120,
        'batches' => [],
        'passPercentage' => 60,
        'hasNegativeMarking' => false,
        'instructions' => 'Test instructions',
        'description' => 'Test description',
        'enableProctoring' => true,
        'proctorConfig' => [
            'webcamMonitoring' => true,
        ],
    ];

    $response = $this->postJson('/api/exam/online-exam', $examData);

    $response->assertStatus(201);
    
    $exam = OnlineExam::where('uuid', $response->json('uuid'))->first();
    expect($exam->enable_proctoring)->toBeTrue();
    expect($exam->proctor_config['webcam_monitoring'])->toBeTrue();
    // Other fields should have default values or be null
});

it('requires authentication to manage proctoring configuration', function () {
    $this->withoutMiddleware();
    
    $examData = [
        'title' => 'Test Exam',
        'enableProctoring' => true,
    ];

    $response = $this->postJson('/api/exam/online-exam', $examData);
    $response->assertStatus(401);
});

it('requires proper permissions to manage proctoring configuration', function () {
    $unauthorizedUser = User::factory()->create(['team_id' => $this->team->id]);
    $this->actingAs($unauthorizedUser);

    $examData = [
        'title' => 'Test Exam',
        'enableProctoring' => true,
    ];

    $response = $this->postJson('/api/exam/online-exam', $examData);
    $response->assertStatus(403);
});
