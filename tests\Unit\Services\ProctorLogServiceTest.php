<?php

use App\Models\Exam\OnlineExam;
use App\Models\Exam\OnlineExamRecord;
use App\Models\Exam\ProctorLog;
use App\Models\Student\Student;
use App\Models\Team;
use App\Services\Exam\ProctorLogService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

beforeEach(function () {
    $this->team = Team::factory()->create();
    $this->student = Student::factory()->create(['team_id' => $this->team->id]);
    $this->exam = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
    ]);
    $this->examRecord = OnlineExamRecord::factory()->create([
        'online_exam_id' => $this->exam->id,
        'student_id' => $this->student->id,
    ]);
    
    $this->service = new ProctorLogService();
    Storage::fake('public');
});

it('can create a proctor log', function () {
    $data = [
        'event_type' => 'webcam_capture',
        'severity' => 'info',
        'description' => 'Webcam image captured',
        'data' => ['face_count' => 1],
        'detected_at' => now(),
        'ip_address' => '***********',
        'user_agent' => 'Mozilla/5.0...',
    ];

    $log = $this->service->create($this->exam, $this->examRecord, $data);

    expect($log)->toBeInstanceOf(ProctorLog::class);
    expect($log->online_exam_id)->toBe($this->exam->id);
    expect($log->online_exam_record_id)->toBe($this->examRecord->id);
    expect($log->student_id)->toBe($this->student->id);
    expect($log->event_type)->toBe('webcam_capture');
    expect($log->severity)->toBe('info');
    expect($log->data)->toBeArray();
    expect($log->data['face_count'])->toBe(1);
});

it('can create a proctor log with media file', function () {
    $file = UploadedFile::fake()->image('webcam.jpg');
    
    $data = [
        'event_type' => 'webcam_capture',
        'severity' => 'info',
        'description' => 'Webcam image captured',
        'data' => ['face_count' => 1],
        'detected_at' => now(),
        'media' => $file,
    ];

    $log = $this->service->create($this->exam, $this->examRecord, $data);

    expect($log->media_path)->not->toBeNull();
    expect($log->hasMedia())->toBeTrue();
    Storage::disk('public')->assertExists($log->media_path);
});

it('can get logs for an exam', function () {
    ProctorLog::factory()->count(5)->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
    ]);

    $logs = $this->service->getLogsForExam($this->exam);

    expect($logs)->toHaveCount(5);
    expect($logs->first())->toBeInstanceOf(ProctorLog::class);
});

it('can get logs for a specific submission', function () {
    $otherExamRecord = OnlineExamRecord::factory()->create([
        'online_exam_id' => $this->exam->id,
        'student_id' => Student::factory()->create(['team_id' => $this->team->id])->id,
    ]);

    ProctorLog::factory()->count(3)->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
    ]);

    ProctorLog::factory()->count(2)->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $otherExamRecord->id,
        'student_id' => $otherExamRecord->student_id,
    ]);

    $logs = $this->service->getLogsForSubmission($this->examRecord);

    expect($logs)->toHaveCount(3);
    expect($logs->first()->online_exam_record_id)->toBe($this->examRecord->id);
});

it('can filter logs by event type', function () {
    ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'event_type' => 'webcam_capture',
    ]);

    ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'event_type' => 'audio_alert',
    ]);

    $logs = $this->service->getLogsForExam($this->exam, ['event_type' => 'webcam_capture']);

    expect($logs)->toHaveCount(1);
    expect($logs->first()->event_type)->toBe('webcam_capture');
});

it('can filter logs by severity', function () {
    ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'severity' => 'critical',
    ]);

    ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'severity' => 'info',
    ]);

    $logs = $this->service->getLogsForExam($this->exam, ['severity' => 'critical']);

    expect($logs)->toHaveCount(1);
    expect($logs->first()->severity)->toBe('critical');
});

it('can get summary for a submission', function () {
    ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'severity' => 'critical',
        'event_type' => 'face_detection_failure',
    ]);

    ProctorLog::factory()->count(2)->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'severity' => 'warning',
        'event_type' => 'tab_switch',
    ]);

    ProctorLog::factory()->count(3)->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'severity' => 'info',
        'event_type' => 'webcam_capture',
    ]);

    $summary = $this->service->getSummaryForSubmission($this->examRecord);

    expect($summary)->toBeArray();
    expect($summary['total_events'])->toBe(6);
    expect($summary['critical_events'])->toBe(1);
    expect($summary['warning_events'])->toBe(2);
    expect($summary['info_events'])->toBe(3);
    expect($summary['event_types'])->toBeArray();
    expect($summary['event_types']['face_detection_failure'])->toBe(1);
    expect($summary['event_types']['tab_switch'])->toBe(2);
    expect($summary['event_types']['webcam_capture'])->toBe(3);
});

it('can generate timeline for a submission', function () {
    $now = now();
    
    ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'event_type' => 'webcam_capture',
        'detected_at' => $now->copy()->subMinutes(10),
        'description' => 'First capture',
    ]);

    ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'event_type' => 'tab_switch',
        'detected_at' => $now->copy()->subMinutes(5),
        'description' => 'Tab switched',
    ]);

    $timeline = $this->service->getTimelineForSubmission($this->examRecord);

    expect($timeline)->toBeArray();
    expect($timeline)->toHaveCount(2);
    expect($timeline[0]['event_type'])->toBe('webcam_capture');
    expect($timeline[1]['event_type'])->toBe('tab_switch');
    // Timeline should be ordered by detected_at
});

it('can export logs as CSV', function () {
    ProctorLog::factory()->count(3)->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
    ]);

    $csv = $this->service->exportLogsAsCsv($this->exam);

    expect($csv)->toBeString();
    expect($csv)->toContain('Event Type,Severity,Description,Detected At');
    expect(substr_count($csv, "\n"))->toBe(4); // Header + 3 data rows + final newline
});

it('stores media files in correct directory structure', function () {
    $file = UploadedFile::fake()->image('webcam.jpg');
    
    $data = [
        'event_type' => 'webcam_capture',
        'severity' => 'info',
        'description' => 'Webcam image captured',
        'detected_at' => now(),
        'media' => $file,
    ];

    $log = $this->service->create($this->exam, $this->examRecord, $data);

    expect($log->media_path)->toContain('proctoring');
    expect($log->media_path)->toContain($this->exam->uuid);
    expect($log->media_path)->toContain($this->examRecord->uuid);
    Storage::disk('public')->assertExists($log->media_path);
});

it('handles different media file types', function () {
    $imageFile = UploadedFile::fake()->image('webcam.jpg');
    $audioFile = UploadedFile::fake()->create('audio.wav', 1000, 'audio/wav');

    $imageData = [
        'event_type' => 'webcam_capture',
        'severity' => 'info',
        'description' => 'Webcam image captured',
        'detected_at' => now(),
        'media' => $imageFile,
    ];

    $audioData = [
        'event_type' => 'audio_alert',
        'severity' => 'warning',
        'description' => 'Audio alert detected',
        'detected_at' => now(),
        'media' => $audioFile,
    ];

    $imageLog = $this->service->create($this->exam, $this->examRecord, $imageData);
    $audioLog = $this->service->create($this->exam, $this->examRecord, $audioData);

    expect($imageLog->media_path)->toContain('.jpg');
    expect($audioLog->media_path)->toContain('.wav');
    Storage::disk('public')->assertExists($imageLog->media_path);
    Storage::disk('public')->assertExists($audioLog->media_path);
});

it('can clean up old logs', function () {
    $oldDate = now()->subDays(100);
    $recentDate = now()->subDays(10);

    ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'created_at' => $oldDate,
    ]);

    ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'created_at' => $recentDate,
    ]);

    $deletedCount = $this->service->cleanupOldLogs(90); // Delete logs older than 90 days

    expect($deletedCount)->toBe(1);
    expect(ProctorLog::count())->toBe(1);
    expect(ProctorLog::first()->created_at->format('Y-m-d'))->toBe($recentDate->format('Y-m-d'));
});
