<?php

namespace App\Http;

use Illuminate\Foundation\Http\Kernel as HttpKernel;

class <PERSON>el extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array<int, class-string|string>
     */
    protected $middleware = [
        // \App\Http\Middleware\TrustHosts::class,
        \App\Http\Middleware\TrustProxies::class,
        \Illuminate\Http\Middleware\HandleCors::class,
        \App\Http\Middleware\PreventRequestsDuringMaintenance::class,
        \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
        \App\Http\Middleware\TrimStrings::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
        \App\Http\Middleware\XssProtection::class,
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array<string, array<int, class-string|string>>
     */
    protected $middlewareGroups = [
        'web' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            // \Illuminate\Session\Middleware\AuthenticateSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\Init::class,
        ],

        'api' => [
            \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
            'throttle:api',
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\Init::class,
        ],
    ];

    /**
     * The application's route middleware.
     *
     * These middleware may be assigned to groups or used individually.
     *
     * @var array<string, class-string|string>
     */
    protected $routeMiddleware = [
        'auth' => \App\Http\Middleware\Authenticate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'password.confirm' => \Illuminate\Auth\Middleware\RequirePassword::class,
        'signed' => \Illuminate\Routing\Middleware\ValidateSignature::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'verified' => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,
        'role' => \Spatie\Permission\Middleware\RoleMiddleware::class,
        'permission' => \Spatie\Permission\Middleware\PermissionMiddleware::class,
        'role_or_permission' => \Spatie\Permission\Middleware\RoleOrPermissionMiddleware::class,
        'optional.auth' => \App\Http\Middleware\OptionalAuthSanctum::class,
        'test.mode.restriction' => \App\Http\Middleware\RestrictedActionInTestMode::class,
        'feature.available' => \App\Http\Middleware\FeatureAvailable::class,
        'module.available' => \App\Http\Middleware\ModuleAvailable::class,
        'two.factor.security' => \App\Http\Middleware\TwoFactorSecurity::class,
        'screen.lock' => \App\Http\Middleware\ScreenLock::class,
        'under.maintenance' => \App\Http\Middleware\UnderMaintenance::class,
        'export' => \App\Http\Middleware\ExportItem::class,
        'user.config' => \App\Http\Middleware\UserConfig::class,
        'super.admin' => \App\Http\Middleware\IsSuperAdmin::class,
        'admin' => \App\Http\Middleware\IsAdmin::class,
        'option.verifier' => \App\Http\Middleware\OptionVerifier::class,
        'site' => \App\Http\Middleware\Site::class,
        'site.enabled' => \App\Http\Middleware\SiteEnabled::class,
        'chat.enabled' => \App\Http\Middleware\ChatEnabled::class,
        'payment.restrictions' => \App\Http\Middleware\CheckPaymentRestrictions::class,
        'check.global.ai.enabled' => \App\Http\Middleware\CheckGlobalAIEnabled::class,
        'proctoring.security' => \App\Http\Middleware\ProctoringSecurity::class,
    ];
}
