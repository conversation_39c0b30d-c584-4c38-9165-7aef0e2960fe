<template>
    <FormAction
        :pre-requisites="true"
        @setPreRequisites="setPreRequisites"
        :init-url="initUrl"
        :init-form="initForm"
        :form="form"
        :set-form="setForm"
        redirect="ExamOnlineExam"
        @resetMediaFiles="resetMediaFiles"
    >
        <div class="grid grid-cols-4 gap-6">
            <div class="col-span-4 sm:col-span-3">
                <BaseInput
                    type="text"
                    v-model="form.title"
                    name="title"
                    :label="$trans('exam.online_exam.props.title')"
                    v-model:error="formErrors.title"
                />
            </div>
            <div class="col-span-4 sm:col-span-1">
                <BaseSelect
                    v-model="form.type"
                    name="type"
                    :label="$trans('exam.online_exam.props.type')"
                    :options="preRequisites.types"
                    v-model:error="formErrors.type"
                />
            </div>
            <div class="col-span-4">
                <BaseSwitch
                    vertical
                    v-model="form.isFlexibleTiming"
                    name="isFlexibleTiming"
                    :label="$trans('exam.online_exam.flexible_timing')"
                    :description="$trans('exam.online_exam.flexible_timing_help')"
                    v-model:error="formErrors.isFlexibleTiming"
                />
            </div>
            <!-- Traditional Timing Fields -->
            <template v-if="!form.isFlexibleTiming">
                <div class="col-span-4 sm:col-span-1">
                    <DatePicker
                        v-model="form.date"
                        name="date"
                        :label="$trans('exam.online_exam.props.date')"
                        no-clear
                        v-model:error="formErrors.date"
                    />
                </div>
                <div class="col-span-4 sm:col-span-1">
                    <DatePicker
                        v-model="form.startTime"
                        name="startTime"
                        :label="$trans('exam.online_exam.props.start_time')"
                        as="time"
                        v-model:error="formErrors.startTime"
                    />
                </div>
                <div class="col-span-4 sm:col-span-1">
                    <DatePicker
                        v-model="form.endDate"
                        name="endDate"
                        :label="$trans('exam.online_exam.props.end_date')"
                        no-clear
                        v-model:error="formErrors.endDate"
                    />
                    <HelperText>{{
                        $trans("exam.online_exam.end_date_info")
                    }}</HelperText>
                </div>
                <div class="col-span-4 sm:col-span-1">
                    <DatePicker
                        v-model="form.endTime"
                        name="endTime"
                        :label="$trans('exam.online_exam.props.end_time')"
                        as="time"
                        v-model:error="formErrors.endTime"
                    />
                </div>
            </template>

            <!-- Flexible Timing Fields -->
            <template v-if="form.isFlexibleTiming">
                <div class="col-span-4 sm:col-span-1">
                    <DatePicker
                        v-model="form.date"
                        name="date"
                        :label="$trans('exam.online_exam.props.date')"
                        no-clear
                        v-model:error="formErrors.date"
                    />
                    <HelperText>Start Date</HelperText>
                </div>
                <div class="col-span-4 sm:col-span-1">
                    <DatePicker
                        v-model="form.startTime"
                        name="startTime"
                        :label="$trans('exam.online_exam.props.start_time')"
                        as="time"
                        v-model:error="formErrors.startTime"
                    />
                </div>
                <div class="col-span-4 sm:col-span-1">
                    <BaseInput
                        type="number"
                        v-model="form.durationMinutes"
                        name="durationMinutes"
                        :label="$trans('exam.online_exam.duration_minutes')"
                        v-model:error="formErrors.durationMinutes"
                        min="1"
                        max="480"
                    />
                    <HelperText>Maximum Duration In Minutes</HelperText>
                </div>
                <div class="col-span-4 sm:col-span-1">
                    <DatePicker
                        v-model="form.expiryDate"
                        name="expiryDate"
                        :label="$trans('exam.online_exam.expiry_date')"
                        v-model:error="formErrors.expiryDate"
                    />
                    <HelperText>Optional: Leave blank for no expiry</HelperText>
                </div>
                <div class="col-span-4 sm:col-span-1">
                    <DatePicker
                        v-model="form.expiryTime"
                        name="expiryTime"
                        :label="$trans('exam.online_exam.expiry_time')"
                        as="time"
                        v-model:error="formErrors.expiryTime"
                    />
                    <HelperText>Optional: Defaults to 23:59:59</HelperText>
                </div>
                <div class="col-span-4">
                    <BaseSwitch
                        vertical
                        v-model="form.autoPublishResultsForFlexibleTiming"
                        name="autoPublishResultsForFlexibleTiming"
                        :label="$trans('exam.online_exam.auto_publish_results')" 
                        :description="$trans('exam.online_exam.auto_publish_results_help')"
                        v-model:error="formErrors.autoPublishResultsForFlexibleTiming"
                    />
                </div>
            </template>
        </div>
        <div class="mt-4 grid grid-cols-4 gap-6">
            <div class="col-span-4 sm:col-span-1">
                <BaseSelectSearch
                    v-if="fetchData.isLoaded"
                    multiple
                    name="batches"
                    :label="$trans('academic.batch.batch')"
                    v-model="form.batches"
                    v-model:error="formErrors.batches"
                    value-prop="uuid"
                    :init-search="fetchData.batches"
                    search-key="course_batch"
                    search-action="academic/batch/list"
                >
                    <template #selectedOption="slotProps">
                        {{ slotProps.value.course.name }} -
                        {{ slotProps.value.name }}
                    </template>

                    <template #listOption="slotProps">
                        {{ slotProps.option.course.nameWithTerm }} -
                        {{ slotProps.option.name }}
                    </template>
                </BaseSelectSearch>
            </div>
            <div class="col-span-4 sm:col-span-1">
                <BaseSelect
                    v-model="form.subject"
                    name="subject"
                    :label="$trans('academic.subject.subject')"
                    label-prop="name"
                    value-prop="uuid"
                    :options="preRequisites.subjects"
                    v-model:error="formErrors.subject"
                />
            </div>
        </div>
        <div class="mt-4 grid grid-cols-4 gap-6">
            <div class="col-span-4 sm:col-span-1">
                <BaseInput
                    type="number"
                    v-model="form.passPercentage"
                    name="passPercentage"
                    :label="$trans('exam.online_exam.props.pass_percentage')"
                    v-model:error="formErrors.passPercentage"
                />
            </div>
            <div class="col-span-4 sm:col-span-1">
                <BaseSwitch
                    vertical
                    v-model="form.hasNegativeMarking"
                    name="hasNegativeMarking"
                    :label="
                        $trans('exam.online_exam.props.has_negative_marking')
                    "
                    v-model:error="formErrors.hasNegativeMarking"
                />
            </div>
            <div
                class="col-span-4 sm:col-span-1"
                v-if="form.hasNegativeMarking"
            >
                <BaseInput
                    type="number"
                    v-model="form.negativeMarkPercentPerQuestion"
                    name="negativeMarkPercentPerQuestion"
                    :label="
                        $trans(
                            'exam.online_exam.props.negative_mark_percent_per_question'
                        )
                    "
                    v-model:error="formErrors.negativeMarkPercentPerQuestion"
                />
            </div>
            <div class="col-span-4">
                <BaseEditor
                    v-model="form.instructions"
                    name="instructions"
                    :edit="route.params.uuid ? true : false"
                    :label="$trans('exam.online_exam.props.instructions')"
                    v-model:error="formErrors.instructions"
                />
            </div>
            <div class="col-span-4">
                <BaseEditor
                    v-model="form.description"
                    name="description"
                    :edit="route.params.uuid ? true : false"
                    :label="$trans('exam.online_exam.props.description')"
                    v-model:error="formErrors.description"
                />
            </div>
        </div>

        <!-- Proctoring Configuration Section -->
        <div class="mt-6">
            <div class="border-b border-gray-200 dark:border-gray-700 pb-2 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    {{ $trans("exam.proctoring.config.title") || "Proctoring Configuration" }}
                </h3>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    {{ $trans("exam.proctoring.config.description") || "Configure monitoring and security features for this exam" }}
                </p>
            </div>

            <!-- Proctoring Configuration - Inline following existing form patterns -->
            <div class="space-y-6">
                <!-- Main Proctoring Toggle -->
                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <BaseSwitch
                        vertical
                        v-model="form.enableProctoring"
                        name="enableProctoring"
                        :label="$trans('exam.proctoring.config.enable_proctoring') || 'Enable Proctoring'"
                        :description="$trans('exam.proctoring.config.enable_proctoring_help') || 'Enable comprehensive monitoring and security features for this exam'"
                        v-model:error="formErrors.enableProctoring"
                    />
                </div>

                <!-- Proctoring Configuration Options -->
                <div v-if="form.enableProctoring" class="space-y-6">
                    <!-- Monitoring Features -->
                    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            {{ $trans("exam.proctoring.config.monitoring_features") || "Monitoring Features" }}
                        </h3>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Webcam Monitoring -->
                            <div class="space-y-3">
                                <BaseSwitch
                                    v-model="form.proctorConfig.webcamMonitoring"
                                    name="webcamMonitoring"
                                    :label="$trans('exam.proctoring.config.webcam_monitoring') || 'Webcam Monitoring'"
                                    :description="$trans('exam.proctoring.config.webcam_monitoring_help') || 'Capture periodic images from student webcam'"
                                />

                                <div v-if="form.proctorConfig.webcamMonitoring" class="ml-6 space-y-3">
                                    <BaseInput
                                        type="number"
                                        v-model="form.proctorConfig.captureIntervalSeconds"
                                        name="captureInterval"
                                        :label="$trans('exam.proctoring.config.capture_interval') || 'Capture Interval (seconds)'"
                                        min="10"
                                        max="300"
                                        class="max-w-xs"
                                    />
                                </div>
                            </div>

                            <!-- Microphone Monitoring -->
                            <div class="space-y-3">
                                <BaseSwitch
                                    v-model="form.proctorConfig.microphoneMonitoring"
                                    name="microphoneMonitoring"
                                    :label="$trans('exam.proctoring.config.microphone_monitoring') || 'Microphone Monitoring'"
                                    :description="$trans('exam.proctoring.config.microphone_monitoring_help') || 'Monitor audio levels and detect external voices'"
                                />

                                <div v-if="form.proctorConfig.microphoneMonitoring" class="ml-6 space-y-3">
                                    <BaseInput
                                        type="number"
                                        v-model="form.proctorConfig.audioThresholdDb"
                                        name="audioThreshold"
                                        :label="$trans('exam.proctoring.config.audio_threshold') || 'Audio Threshold (dB)'"
                                        min="-80"
                                        max="0"
                                        class="max-w-xs"
                                    />
                                </div>
                            </div>

                            <!-- Face Detection -->
                            <div class="space-y-3">
                                <BaseSwitch
                                    v-model="form.proctorConfig.faceDetection"
                                    name="faceDetection"
                                    :label="$trans('exam.proctoring.config.face_detection') || 'Face Detection'"
                                    :description="$trans('exam.proctoring.config.face_detection_help') || 'AI-powered face detection and identity verification'"
                                />

                                <div v-if="form.proctorConfig.faceDetection" class="ml-6 space-y-3">
                                    <BaseInput
                                        type="number"
                                        v-model="form.proctorConfig.maxFaceDetectionFailures"
                                        name="maxFaceFailures"
                                        :label="$trans('exam.proctoring.config.max_face_failures') || 'Max Face Detection Failures'"
                                        min="1"
                                        max="20"
                                        class="max-w-xs"
                                    />
                                </div>
                            </div>

                            <!-- Screen Recording -->
                            <div class="space-y-3">
                                <BaseSwitch
                                    v-model="form.proctorConfig.screenRecording"
                                    name="screenRecording"
                                    :label="$trans('exam.proctoring.config.screen_recording') || 'Screen Recording'"
                                    :description="$trans('exam.proctoring.config.screen_recording_help') || 'Record screen activity and detect tab switching'"
                                />
                            </div>
                        </div>
                    </div>

                    <!-- Security Features -->
                    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            {{ $trans("exam.proctoring.config.security_features") || "Security Features" }}
                        </h3>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Fullscreen Enforcement -->
                            <div class="space-y-3">
                                <BaseSwitch
                                    v-model="form.proctorConfig.fullscreenEnforcement"
                                    name="fullscreenEnforcement"
                                    :label="$trans('exam.proctoring.config.fullscreen_enforcement') || 'Fullscreen Enforcement'"
                                    :description="$trans('exam.proctoring.config.fullscreen_enforcement_help') || 'Force exam to run in fullscreen mode'"
                                />
                            </div>

                            <!-- Copy-Paste Blocking -->
                            <div class="space-y-3">
                                <BaseSwitch
                                    v-model="form.proctorConfig.copyPasteBlocking"
                                    name="copyPasteBlocking"
                                    :label="$trans('exam.proctoring.config.copy_paste_blocking') || 'Copy-Paste Blocking'"
                                    :description="$trans('exam.proctoring.config.copy_paste_blocking_help') || 'Block copy, cut, and paste operations'"
                                />
                            </div>

                            <!-- Tab Switching -->
                            <div class="space-y-3">
                                <BaseSwitch
                                    v-model="form.proctorConfig.allowTabSwitching"
                                    name="allowTabSwitching"
                                    :label="$trans('exam.proctoring.config.allow_tab_switching') || 'Allow Tab Switching'"
                                    :description="$trans('exam.proctoring.config.allow_tab_switching_help') || 'Allow students to switch between browser tabs'"
                                />
                            </div>

                            <!-- Auto Submit on Violations -->
                            <div class="space-y-3">
                                <BaseSwitch
                                    v-model="form.proctorConfig.autoSubmitOnViolations"
                                    name="autoSubmitOnViolations"
                                    :label="$trans('exam.proctoring.config.auto_submit_on_violations') || 'Auto Submit on Violations'"
                                    :description="$trans('exam.proctoring.config.auto_submit_on_violations_help') || 'Automatically submit exam when critical violations are detected'"
                                />
                            </div>
                        </div>
                    </div>

                    <!-- Custom Instructions -->
                    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            {{ $trans("exam.proctoring.config.advanced_settings") || "Advanced Settings" }}
                        </h3>

                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    {{ $trans("exam.proctoring.config.custom_instructions") || "Custom Proctoring Instructions" }}
                                </label>
                                <BaseTextarea
                                    v-model="form.proctorConfig.customInstructions"
                                    name="customInstructions"
                                    :placeholder="$trans('exam.proctoring.config.custom_instructions_placeholder') || 'Additional instructions for students regarding proctoring requirements...'"
                                    :rows="3"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-4 grid grid-cols-1">
            <div class="col">
                <MediaUpload
                    multiple
                    :label="$trans('general.file')"
                    module="online_exam"
                    :media="form.media"
                    :media-token="form.mediaToken"
                    @isUpdated="form.mediaUpdated = true"
                    @setHash="(hash) => form.mediaHash.push(hash)"
                />
            </div>
        </div>
    </FormAction>
</template>

<script>
export default {
    name: "ExamOnlineExamForm",
}
</script>

<script setup>
import { reactive } from "vue"
import { useRoute } from "vue-router"
import { cloneDeep } from "@core/utils"
import { getFormErrors } from "@core/helpers/action"
import { v4 as uuidv4 } from "uuid"


const route = useRoute()

const initForm = {
    title: "",
    type: "",
    isFlexibleTiming: false,
    date: "",
    startTime: "",
    endDate: "",
    endTime: "",
    durationMinutes: null,
    expiryDate: "",
    expiryTime: "",
    autoPublishResultsForFlexibleTiming: true,
    batches: [],
    subject: "",
    passPercentage: 0,
    hasNegativeMarking: false,
    negativeMarkPercentPerQuestion: 0,
    instructions: "",
    description: "",
    enableProctoring: false,
    proctorConfig: {
        webcamMonitoring: true,
        microphoneMonitoring: true,
        screenRecording: true,
        fullscreenEnforcement: true,
        copyPasteBlocking: true,
        faceDetection: true,
        captureIntervalSeconds: 30,
        audioThresholdDb: -40,
        maxFaceDetectionFailures: 5,
        allowTabSwitching: false,
        autoSubmitOnViolations: false,
        customInstructions: ''
    },
    media: [],
    mediaUpdated: false,
    mediaToken: uuidv4(),
    mediaHash: [],
}

const initUrl = "exam/onlineExam/"
const formErrors = getFormErrors(initUrl)
const preRequisites = reactive({
    types: [],
    subjects: [],
})

const form = reactive({ ...initForm })
const fetchData = reactive({
    batches: [],
    subject: "",
    isLoaded: route.params.uuid ? false : true,
})

// Proctoring configuration is now directly managed in form object
// Following the standard pattern used by other complex forms

const setPreRequisites = (data) => {
    Object.assign(preRequisites, data)
}

const resetMediaFiles = () => {
    form.mediaToken = uuidv4()
    form.mediaHash = []
}

const setForm = (data) => {
    let batches =
        data.records.map((record) => {
            return record.batch.uuid
        }) || []

    Object.assign(initForm, {
        ...data,
        type: data.type.value,
        isFlexibleTiming: Boolean(data.isFlexibleTiming),
        date: data.date.value,
        startTime: data.startTime.at,
        endDate: data.endDate.value,
        endTime: data.endTime.at,
        durationMinutes: data.durationMinutes,
        expiryDate: data.expiryDate?.value || "",
        expiryTime: data.expiryTime?.at || "",
        autoPublishResultsForFlexibleTiming: data.autoPublishResultsForFlexibleTiming ?? true,
        passPercentage: data.passPercentage.value,
        batches,
        subject: data.records[0]?.subject?.uuid || "",
        enableProctoring: Boolean(data.enableProctoring),
        proctorConfig: { 
            ...initForm.proctorConfig, 
            allowTabSwitching: Boolean(data.proctorConfig.allowTabSwitching),
            audioThresholdDb: Number(data.proctorConfig.audioThresholdDb),
            autoSubmitOnViolations: Boolean(data.proctorConfig.autoSubmitOnViolations),
            captureIntervalSeconds: Number(data.proctorConfig.captureIntervalSeconds),
            copyPasteBlocking: Boolean(data.proctorConfig.copyPasteBlocking),
            customInstructions: data.proctorConfig.customInstructions,
            faceDetection: Boolean(data.proctorConfig.faceDetection),
            fullscreenEnforcement: Boolean(data.proctorConfig.fullscreenEnforcement),
            maxFaceDetectionFailures: Number(data.proctorConfig.maxFaceDetectionFailures),
            microphoneMonitoring: Boolean(data.proctorConfig.microphoneMonitoring),
            screenRecording: Boolean(data.proctorConfig.screenRecording),
            webcamMonitoring: Boolean(data.proctorConfig.webcamMonitoring),
            //...data.proctorConfig 
        },
    })
    Object.assign(form, cloneDeep(initForm))

    fetchData.batches = batches

    fetchData.isLoaded = true
}
</script>
