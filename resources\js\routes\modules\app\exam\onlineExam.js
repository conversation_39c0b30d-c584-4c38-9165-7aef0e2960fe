import question from "./question"
import submission from "./submission"

export default [
    {
        path: "online-exams",
        name: "ExamOnlineExam",
        redirect: { name: "ExamOnlineExamList" },
        meta: {
            label: "exam.online_exam.online_exam",
            icon: "fas fa-right-long",
            hideChildren: true,
            permissions: ["online-exam:read"],
            keySearch: true,
            keySearchLabel: "exam.online_exam.online_exam",
        },
        component: {
            template: "<router-view></router-view>",
        },
        children: [
            {
                path: "",
                name: "ExamOnlineExamList",
                meta: {
                    trans: "global.list",
                    label: "exam.online_exam.online_exams",
                },
                component: () =>
                    import("@views/Pages/Exam/OnlineExam/Index.vue"),
            },
            {
                path: "create",
                name: "ExamOnlineExamCreate",
                meta: {
                    type: "create",
                    action: "create",
                    trans: "global.add",
                    label: "exam.online_exam.online_exam",
                    permission: ["online-exam:create"],
                },
                component: () =>
                    import("@views/Pages/Exam/OnlineExam/Action.vue"),
            },
            {
                path: ":uuid/edit",
                name: "ExamOnlineExamEdit",
                meta: {
                    type: "edit",
                    action: "update",
                    trans: "global.edit",
                    label: "exam.online_exam.online_exam",
                    permission: ["online-exam:edit"],
                },
                component: () =>
                    import("@views/Pages/Exam/OnlineExam/Action.vue"),
            },
            {
                path: ":uuid/duplicate",
                name: "ExamOnlineExamDuplicate",
                meta: {
                    type: "duplicate",
                    action: "create",
                    trans: "global.duplicate",
                    label: "exam.online_exam.online_exam",
                    permission: ["online-exam:create"],
                },
                component: () =>
                    import("@views/Pages/Exam/OnlineExam/Action.vue"),
            },
            {
                path: "test-proctoring",
                name: "ExamOnlineExamTestProctoring",
                meta: {
                    trans: "Test Proctoring",
                    label: "Test Proctoring",
                    permissions: ["online-exam:manage"],
                },
                component: () =>
                    import("@views/Pages/Exam/OnlineExam/TestProctoring.vue"),
            },
            {
                path: ":uuid/submit",
                name: "ExamOnlineExamSubmit",
                meta: {
                    trans: "exam.online_exam.submit",
                    label: "exam.online_exam.submit",
                    role: ["student"],
                },
                component: () =>
                    import("@views/Pages/Exam/OnlineExam/Submit.vue"),
            },
            {
                path: ":uuid/submission",
                name: "ExamOnlineExamStudentSubmission",
                meta: {
                    trans: "exam.online_exam.result",
                    label: "exam.online_exam.result",
                    role: ["student", "guardian"],
                },
                component: () =>
                    import("@views/Pages/Exam/OnlineExam/Submission.vue"),
            },
            {
                path: ":uuid",
                name: "ExamOnlineExamShow",
                redirect: { name: "ExamOnlineExamShowGeneral" },
                meta: {
                    trans: "global.detail",
                    label: "exam.online_exam.online_exam",
                    permissions: ["online-exam:read"],
                },
                component: () =>
                    import("@views/Pages/Exam/OnlineExam/Show.vue"),
                children: [
                    {
                        path: "",
                        name: "ExamOnlineExamShowIndex",
                        redirect: { name: "ExamOnlineExamShowGeneral" },
                    },
                    {
                        path: "general",
                        name: "ExamOnlineExamShowGeneral",
                        meta: {
                            type: "read",
                            action: "read",
                            trans: "general.general",
                            label: "general.general",
                        },
                        component: () =>
                            import("@views/Pages/Exam/OnlineExam/General.vue"),
                    },
                    {
                        path: "questions",
                        name: "ExamOnlineExamQuestion",
                        redirect: { name: "ExamOnlineExamQuestionList" },
                        meta: {},
                        component: {
                            template: "<router-view></router-view>",
                        },
                        children: [...question],
                    },
                    {
                        path: "submissions",
                        name: "ExamOnlineExamSubmission",
                        redirect: { name: "ExamOnlineExamSubmissionList" },
                        meta: {},
                        component: {
                            template: "<router-view></router-view>",
                        },
                        children: [...submission],
                    },
                ],
            },
        ],
    },
]
