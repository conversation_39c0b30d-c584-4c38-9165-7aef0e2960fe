<template>
    <PageHeader
        :title="onlineExam.title"
        :navs="[
            {
                label: $trans('exam.exam'),
                path: 'Exam',
            },
            {
                label: $trans('exam.online_exam.online_exam'),
                path: 'ExamOnlineExam',
            },
        ]"
    >
        <PageHeaderAction
            name="ExamOnlineExam"
            :title="$trans('exam.online_exam.online_exam')"
            :actions="['list']"
        />
    </PageHeader>

        <!-- <PERSON><PERSON> Header with Countdown Timer -->
    <div
        v-if="onlineExam.uuid && onlineExam.startedAt.value && !onlineExam.submittedAt.value"
        class="sticky top-0 z-50 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm"
    >
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
                        {{ onlineExam.title }}
                    </h1>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- Proctoring Status Indicators -->
                    <ProctorStatusIndicator
                        v-if="onlineExam.enableProctoring"
                        :is-active="proctorStatus.isActive"
                        :config="onlineExam.proctorConfig || {}"
                        :status="proctorStatus"
                        :event-counts="proctorStatus.eventCounts"
                    />

                    <!-- Timer -->
                    <div class="flex items-center space-x-2" :class="{ 'animate-pulse': isTimeRunningLow }">
                        <svg class="w-5 h-5" :class="isTimeRunningLow ? 'text-red-600' : 'text-red-500'" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-xl font-bold" :class="isTimeRunningLow ? 'text-red-700 dark:text-red-300' : 'text-red-600 dark:text-red-400'">
                            {{ remainingCountdownTimeInMinutes }}
                        </span>
                    </div>
                    <span class="text-sm text-gray-500 dark:text-gray-400">
                        {{ $trans("exam.online_exam.remaining_time_label") || "Time Left" }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <ParentTransition appear :visibility="true" v-if="onlineExam.uuid">
        <BaseCard :is-loading="isLoading">
            <template #title>
                {{ onlineExam.title }}
            </template>
            <template #action>
                <span class="text-sm" v-if="!onlineExam.submittedAt.value">
                    <!-- <span class="font-semibold">{{
                        formatTime(currentTime)
                    }}</span> -->
                    <!-- <span class="ml-2 text-danger"
                        >({{
                            $trans("exam.online_exam.remaining_time", {
                                attribute: remainingCountdownTimeInMinutes,
                            })
                        }})</span
                    > -->
                    <!-- <span class="text-lg text-danger font-semibold">{{
                        remainingCountdownTimeInMinutes
                    }}</span> -->
                    <span
                        class="dark:text-gray-400"
                        v-html="state.instructions"
                    ></span>
                </span>
            </template>

            <!-- <dl class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
                <BaseDataView :label="$trans('exam.online_exam.props.period')">
                    {{ onlineExam.startTime.formatted }} -
                    {{ onlineExam.endTime.formatted }}
                </BaseDataView>
            </dl> -->

            <div v-if="state.instructions && !onlineExam.startedAt.value">
                <div
                    class="dark:text-gray-400"
                    v-html="state.instructions"
                ></div>

                <!-- Proctoring Setup Section -->
                <ProctorSetupCard
                    v-if="onlineExam.enableProctoring"
                    :config="onlineExam.proctorConfig || {}"
                    :is-initialized="proctorStatus.isInitialized"
                    :is-initializing="!proctorStatus.isInitialized && proctorStatus.errors.length === 0"
                    :errors="proctorStatus.errors"
                    @test-proctoring="initializeProctoring"
                    class="mt-6"
                />

                <BaseButton
                    design="info"
                    block
                    class="mt-4"
                    @click="startSubmission"
                    :disabled="onlineExam.enableProctoring && !proctorStatus.isInitialized"
                    >{{
                        onlineExam.enableProctoring && !proctorStatus.isInitialized
                            ? ($trans("exam.proctoring.initializing") || "Initializing Proctoring...")
                            : $trans("exam.online_exam.start_submission")
                    }}</BaseButton
                >
            </div>

            <div
                v-if="
                    form.questions.length &&
                    onlineExam.startedAt.value &&
                    !onlineExam.submittedAt.value
                "
            >
                <FormAction
                    no-card
                    no-data-fetch
                    action="submit"
                    :keep-adding="false"
                    :init-url="initUrl"
                    :init-form="initForm"
                    :form="form"
                    :stay-on="true"
                    :after-submit="afterSubmit"
                >
                    <div class="grid grid-cols-1 gap-6">
                        <div
                            class="col-span-3 sm:col-span-1"
                            v-for="(question, index) in form.questions"
                            :key="question.uuid"
                        >
                            <div class="mb-4" v-if="question.header">
                                <div
                                    class="text-lg font-semibold dark:text-gray-300"
                                    v-html="question.header"
                                ></div>
                            </div>

                            <div class="dark:text-gray-400 flex items-center">
                                <span
                                    class="mr-2 text-lg text-gray-600 dark:text-gray-300"
                                    >{{ index + 1 }}.</span
                                >
                                <div v-html="question.title"></div>
                            </div>

                            <BaseInput
                                v-if="
                                    question.type.value ==
                                    'single_line_question'
                                "
                                type="text"
                                v-model="question.answer"
                                :name="`questions.${question.name}`"
                                :placeholder="
                                    $trans('exam.online_exam.props.answer')
                                "
                                v-model:error="
                                    formErrors[`questions.${question.name}`]
                                "
                            />
                            <BaseTextarea
                                v-if="
                                    question.type.value == 'multi_line_question'
                                "
                                v-model="question.answer"
                                :name="`questions.${question.name}`"
                                :placeholder="
                                    $trans('exam.online_exam.props.answer')
                                "
                                v-model:error="
                                    formErrors[`questions.${question.name}`]
                                "
                            />
                            <BaseRadioGroup
                                v-if="question.type.value == 'mcq'"
                                top-margin
                                :options="question.options"
                                v-model="question.answer"
                                :name="`questions.${question.name}`"
                                :placeholder="question.title"
                                v-model:error="
                                    formErrors[`questions.${question.name}`]
                                "
                                horizontal
                            />
                        </div>
                    </div>
                </FormAction>
            </div>

            <div v-if="onlineExam.submittedAt.value">
                <div class="grid grid-cols-1 gap-6">
                    <div
                        class="col-span-3 sm:col-span-1"
                        v-for="(question, index) in form.questions"
                        :key="question.uuid"
                    >
                        <div class="mb-4" v-if="question.header">
                            <div
                                class="text-lg font-semibold dark:text-gray-300"
                                v-html="question.header"
                            ></div>
                        </div>

                        <div class="dark:text-gray-400 flex items-center">
                            <span
                                class="mr-2 text-lg text-gray-600 dark:text-gray-300"
                                >{{ index + 1 }}.</span
                            >
                            <div v-html="question.title"></div>
                        </div>

                        <div
                            class="dark:text-gray-300 font-semibold text-sm italic"
                        >
                            <pre>{{ question.answer }}</pre>
                        </div>
                    </div>
                </div>
            </div>

            <div
                class="mt-4"
                v-if="
                    // state.submittedAnswerCount === form.questions.length &&
                    // !onlineExam.submittedAt.value
                    onlineExam.startedAt.value && !onlineExam.submittedAt.value
                "
            >
                <p class="text-sm my-2 dark:text-gray-300">
                    {{ $trans("exam.online_exam.finish_submission_info") }}
                </p>
                <BaseButton design="success" block @click="finishSubmission">
                    {{ $trans("exam.online_exam.finish_submission") }}
                </BaseButton>
            </div>
        </BaseCard>

        <!-- Proctoring Dashboard -->
        <ProctorDashboard
            v-if="onlineExam.enableProctoring"
            :is-active="proctorStatus.isActive"
            :config="onlineExam.proctorConfig || {}"
            :status="proctorStatus"
            :event-counts="proctorStatus.eventCounts"
            @test-camera="testCamera"
            @test-microphone="testMicrophone"
        />

        <!-- Proctoring Alerts -->
        <div class="fixed top-20 right-4 z-50 space-y-2">
            <ProctorAlert
                v-for="alert in proctorAlerts"
                :key="alert.id"
                :type="alert.type"
                :title="alert.title"
                :message="alert.message"
                :actions="alert.actions"
                :persistent="alert.persistent"
                @close="removeAlert(alert.id)"
                @action="handleAlertAction"
            />
        </div>

        <!-- Proctoring Requirements Check Modal -->
        <ProctorRequirementsCheck
            :visibility="showRequirementsCheck"
            :exam="onlineExam"
            @close="showRequirementsCheck = false"
            @requirements-met="onRequirementsMet"
        />
    </ParentTransition>
</template>

<script>
export default {
    name: "ExamOnlineExamSubmit",
}
</script>

<script setup>
import { reactive, ref, onMounted, computed, onUnmounted, watch } from "vue"
import { useRoute, useRouter } from "vue-router"
import { useStore } from "vuex"
import { cloneDeep } from "@core/utils"
import { v4 as uuidv4 } from "uuid"
import { getFormErrors } from "@core/helpers/action"
import { confirmAction } from "@core/helpers/alert"
import { useProctoring } from "@core/composables/useProctoring"
import { useExamSecurity } from "@core/composables/useExamSecurity"
import ProctorSetupCard from "@core/components/Exam/ProctorSetupCard.vue"
import ProctorDashboard from "@core/components/Exam/ProctorDashboard.vue"
import ProctorAlert from "@core/components/Exam/ProctorAlert.vue"
import ProctorStatusIndicator from "@core/components/Exam/ProctorStatusIndicator.vue"
import ProctorRequirementsCheck from "@core/components/Exam/ProctorRequirementsCheck.vue"

const store = useStore()
const route = useRoute()
const router = useRouter()

const initUrl = "exam/onlineExam/"
const formErrors = getFormErrors(initUrl)

const isLoading = ref(false)

const initForm = {
    questions: [],
    mediaUpdated: false,
    mediaToken: uuidv4(),
    mediaHash: [],
}

const form = reactive({ ...initForm })
const onlineExam = reactive({})
const state = reactive({
    instructions: "",
    media: [],
    submittedAnswerCount: 0,
})

const timeStatus = ref({
    started: false,
    remainingTime: null,
    expired: false,
    individualEndTime: null,
    autoSubmitted: false,
})

// Proctoring state
const proctoring = ref(null)
const examSecurity = ref(null)
const showRequirementsCheck = ref(false)
const proctorStatus = reactive({
    isInitialized: false,
    isActive: false,
    errors: [],
    webcamStatus: 'inactive',
    microphoneStatus: 'inactive',
    faceDetectionStatus: 'inactive',
    fullscreenStatus: 'inactive',
    eventCounts: {
        webcamCaptures: 0,
        audioAlerts: 0,
        tabSwitches: 0,
        fullscreenExits: 0,
        copyPasteAttempts: 0,
        faceDetectionFailures: 0
    }
})

// Proctoring alerts
const proctorAlerts = ref([])

// Store the initial remaining time and start time for client-side countdown
const initialRemainingTime = ref(null)
const countdownStartTime = ref(null)

const currentTime = ref(new Date())
const timerInterval = ref(null)

const remainingCountdownTimeInMinutes = computed(() => {
    // For exams that haven't started yet, show 0:00:00
    if (!onlineExam.startedAt?.value) {
        return "0:00:00"
    }

    let remainingSeconds = 0

    if (onlineExam.isFlexibleTiming) {
        // For flexible timing, use client-side countdown based on initial backend time
        if (timeStatus.value.started && initialRemainingTime.value !== null && countdownStartTime.value !== null) {
            // Calculate elapsed time since we got the initial remaining time
            const elapsedSeconds = Math.max(0, Math.floor((currentTime.value - countdownStartTime.value) / 1000))
            remainingSeconds = Math.max(0, initialRemainingTime.value - elapsedSeconds)
        } else {
            // Return a placeholder while waiting for time status
            return "Loading..."
        }
    } else {
        // For traditional timing, use the exam end time - simplified approach
        if (onlineExam.date?.value && onlineExam.endTime?.at) {
            const endTime = new Date(
                onlineExam.date.value + " " + onlineExam.endTime.at
            )
            const diffInMilliseconds = endTime - currentTime.value
            remainingSeconds = Math.max(0, Math.floor(diffInMilliseconds / 1000))
        } else {
            return "0:00:00"
        }
    }

    if (remainingSeconds <= 0) {
        return "0:00:00"
    }

    // Calculate hours, minutes and seconds
    const hours = Math.floor(remainingSeconds / 3600)
    const minutes = Math.floor((remainingSeconds % 3600) / 60)
    const seconds = remainingSeconds % 60

    // Format numbers to always show two digits
    const formattedMinutes = minutes.toString().padStart(2, "0")
    const formattedSeconds = seconds.toString().padStart(2, "0")

    // If time is greater than 60 minutes, show hours
    if (hours > 0) {
        return `${hours}:${formattedMinutes}:${formattedSeconds}`
    }

    // For less than 60 minutes, show only minutes and seconds
    return `${minutes}:${formattedSeconds}`
})

// Check if exam time has expired (client-side)
const isExamExpired = computed(() => {
    if (!onlineExam.startedAt?.value) {
        return false
    }

    if (onlineExam.isFlexibleTiming) {
        // For flexible timing, use client-side calculation
        if (timeStatus.value.started && initialRemainingTime.value !== null && countdownStartTime.value !== null) {
            const elapsedSeconds = Math.max(0, Math.floor((currentTime.value - countdownStartTime.value) / 1000))
            const remainingSeconds = Math.max(0, initialRemainingTime.value - elapsedSeconds)
            return remainingSeconds <= 0
        }
        // Don't consider expired if not started yet or no countdown data
        return false
    } else {
        // For traditional timing, check against exam end time
        if (onlineExam.date?.value && onlineExam.endTime?.at) {
            const endTime = new Date(
                onlineExam.date.value + " " + onlineExam.endTime.at
            )
            return currentTime.value >= endTime
        }
    }

    return false
})

// Check if time is running low (less than 5 minutes remaining)
const isTimeRunningLow = computed(() => {
    if (!onlineExam.startedAt?.value) {
        return false
    }

    let remainingSeconds = 0

    if (onlineExam.isFlexibleTiming) {
        // For flexible timing, use client-side calculation
        if (timeStatus.value.started && initialRemainingTime.value !== null && countdownStartTime.value !== null) {
            const elapsedSeconds = Math.max(0, Math.floor((currentTime.value - countdownStartTime.value) / 1000))
            remainingSeconds = Math.max(0, initialRemainingTime.value - elapsedSeconds)
        }
    } else {
        // For traditional timing, check against exam end time
        if (onlineExam.date?.value && onlineExam.endTime?.at) {
            const endTime = new Date(
                onlineExam.date.value + " " + onlineExam.endTime.at
            )
            const diffInMilliseconds = endTime - currentTime.value
            remainingSeconds = Math.max(0, Math.floor(diffInMilliseconds / 1000))
        }
    }

    // Return true if less than 5 minutes (300 seconds) remaining
    return remainingSeconds > 0 && remainingSeconds <= 300
})


const getOnlineExam = async () => {
    await store
        .dispatch("exam/onlineExam/get", {
            uuid: route.params.uuid,
            params: {
                submission: true,
            },
        })
        .then(async (response) => {
            const responseData = { ...response }

            // Ensure enableProctoring is a proper boolean
            if (responseData.hasOwnProperty('enableProctoring')) {
                responseData.enableProctoring = Boolean(Number(responseData.enableProctoring))
            }

            // Ensure proctorConfig is a proper object
            if (responseData.proctorConfig && typeof responseData.proctorConfig === 'string') {
                try {
                    responseData.proctorConfig = JSON.parse(responseData.proctorConfig)
                } catch (e) {
                    console.error("Failed to parse proctorConfig:", e)
                    responseData.proctorConfig = {} // Fallback to empty object on parsing error
                }
            }
            
            Object.assign(onlineExam, responseData)

            // Check availability based on exam type
            const isAvailable = onlineExam.isFlexibleTiming
                ? onlineExam.isAvailable
                : onlineExam.isLive

            if (!isAvailable) {
                router.push({
                    name: "ExamOnlineExam",
                })
                return
            }

            // Check if already submitted - prevent re-access
            if (onlineExam.submittedAt?.value) {
                router.push({
                    name: "ExamOnlineExam",
                })
                return
            }

            // Initialize proctoring if enabled
            if (onlineExam.enableProctoring) {
                await initializeProctoring()
            }

            getLiveQuestions()
            isLoading.value = false
        })
        .catch((e) => {
            isLoading.value = false
        })
}

const getLiveQuestions = async () => {
    await store
        .dispatch("exam/onlineExam/getLiveQuestions", {
            uuid: route.params.uuid,
        })
        .then((response) => {
            state.instructions = response.instructions
            state.media = response.media
            state.submittedAnswerCount = response.submittedAnswerCount
            response.questions.forEach((question) => {
                initForm.questions.push({
                    uuid: question.uuid,
                    name: question.name,
                    header: question.header,
                    title: question.title,
                    type: question.type,
                    options: question.options,
                    answer: question.answer ?? "",
                })
            })

            Object.assign(form, cloneDeep(initForm))

            isLoading.value = false
        })
        .catch((e) => {
            isLoading.value = false
        })
}

const startSubmission = async () => {
    isLoading.value = true
    try {
        const response = await store.dispatch("exam/onlineExam/startSubmission", {
            uuid: route.params.uuid,
        })

        onlineExam.startedAt = response.startedAt

        // Set submission UUID for proctoring if available
        if (onlineExam.enableProctoring && proctoring.value && response.submissionUuid) {
            // Update proctoring composable with submission UUID
            proctoring.value.submissionUuid = response.submissionUuid
            examSecurity.value.submissionUuid = response.submissionUuid

            // Initialize proctoring composable
            proctoring.value = useProctoring(
                route.params.uuid,
                response.submissionUuid, // Will be set when submission starts
                onlineExam.proctorConfig
            )

            // Initialize security composable
            examSecurity.value = useExamSecurity(
                route.params.uuid,
                response.submissionUuid, // Will be set when submission starts
                onlineExam.proctorConfig
            )

            // Initialize proctoring system
            await proctoring.value.initialize()

            // Initialize security measures
            examSecurity.value.initialize()

            proctorStatus.isInitialized = true
            proctorStatus.webcamStatus = 'ready'
            proctorStatus.microphoneStatus = 'ready'
            proctorStatus.faceDetectionStatus = 'ready'
            proctorStatus.fullscreenStatus = 'ready'


            // Start proctoring monitoring
            await startProctoring()
        }

        // Update time status after starting (only for flexible exams)
        if (onlineExam.isFlexibleTiming) {
            await getTimeStatus()
        }

        isLoading.value = false
    } catch (e) {
        isLoading.value = false
    }
}

const finishSubmission = async (skipConfirmation = false) => {
    if (!(await confirmAction())) {
        return
    }

    // if (!skipConfirmation && !(await confirmAction())) { 
    //     return
    // }

    isLoading.value = true

    await store.dispatch("exam/onlineExam/submit", {
        uuid: route.params.uuid,
        form: {
            questions: form.questions,
        },
    })

    store
        .dispatch("exam/onlineExam/finishSubmission", {
            uuid: route.params.uuid,
        })
        .then((response) => {
            onlineExam.submittedAt = response.submittedAt

            // Stop proctoring when exam is submitted
            if (onlineExam.enableProctoring) {
                stopProctoring()
            }

            // Handle post-submission flow for manual submissions
            handlePostSubmissionFlow()

            isLoading.value = false
        })
        .catch((e) => {
            isLoading.value = false
        })
}

const autoSubmitExam = async (retryCount = 0) => {
    const maxRetries = 3
    const retryDelay = 1000 // 1 second

    try {
        // First, submit current answers with auto_submit flag to bypass time validation
        await store.dispatch("exam/onlineExam/submit", {
            uuid: route.params.uuid,
            form: {
                questions: form.questions,
                auto_submit: true, // This bypasses time validation
            },
        })

        // Then finish the submission with auto_submit flag
        const response = await store.dispatch("exam/onlineExam/finishSubmission", {
            uuid: route.params.uuid,
            auto_submit: true, // This marks it as auto-submitted
        })

        // Update the submission status
        onlineExam.submittedAt = response.submittedAt

        // Handle post-submission flow with auto-publish consideration
        handleAutoSubmitPostFlow()

    } catch (error) {
        console.error(`Auto-submit attempt ${retryCount + 1} failed:`, error)

        // Check if it's a network error and we can retry
        if (isNetworkError(error) && retryCount < maxRetries) {
            console.log(`Retrying auto-submit in ${retryDelay}ms... (attempt ${retryCount + 2}/${maxRetries + 1})`)
            setTimeout(() => {
                autoSubmitExam(retryCount + 1)
            }, retryDelay * (retryCount + 1)) // Exponential backoff
            return
        }

        // If all retries failed or it's not a network error, try emergency fallback
        console.error("All auto-submit attempts failed, trying emergency submission...")
        await emergencySubmission()
    }
}

const isNetworkError = (error) => {
    // Check for common network error indicators
    return error.code === 'NETWORK_ERROR' ||
           error.message?.includes('Network Error') ||
           error.message?.includes('timeout') ||
           error.status === 0 ||
           !navigator.onLine
}

const emergencySubmission = async () => {
    try {
        // Emergency: Try to submit answers one more time with auto_submit flag
        await store.dispatch("exam/onlineExam/submit", {
            uuid: route.params.uuid,
            form: {
                questions: form.questions,
                auto_submit: true,
            },
        })

        // Then finish submission
        const response = await store.dispatch("exam/onlineExam/finishSubmission", {
            uuid: route.params.uuid,
            auto_submit: true,
        })

        onlineExam.submittedAt = response.submittedAt
        handleAutoSubmitPostFlow()

    } catch (emergencyError) {
        console.error("Emergency submission with answers failed:", emergencyError)

        // Last resort: finish submission without answers (but with auto_submit flag)
        try {
            const response = await store.dispatch("exam/onlineExam/finishSubmission", {
                uuid: route.params.uuid,
                auto_submit: true,
            })

            onlineExam.submittedAt = response.submittedAt

            // Show warning to user that answers might not have been saved
            if (window.toast) {
                window.toast.warning("Exam submitted due to time expiry, but some answers may not have been saved due to connection issues.")
            }

            handleAutoSubmitPostFlow()

        } catch (lastResortError) {
            console.error("Last resort submission also failed:", lastResortError)
            // At this point, we can't do much more - the exam time has expired
            // Redirect to main page with error message
            if (window.toast) {
                window.toast.error("Unable to submit exam due to connection issues. Please contact support.")
            }
            router.push({ name: "ExamOnlineExam" })
        }
    }
}

const handleAutoSubmitPostFlow = () => {
    // For flexible timing exams with auto-publish, redirect to results immediately
    if (onlineExam.isFlexibleTiming && onlineExam.autoPublishResultsForFlexibleTiming) {
        // For MCQ exams, results should be available immediately - redirect to results
        if (onlineExam.type?.value === 'mcq') {
            // For auto-submit, redirect directly to results page since results are auto-published
            router.push({
                name: "ExamOnlineExamSubmission",
                params: { uuid: route.params.uuid }
            })
        } else {
            // For mixed/essay exams, redirect to main page (results will be published after manual evaluation)
            router.push({
                name: "ExamOnlineExam",
            })
        }
    } else {
        // For traditional exams or manual publish, redirect to main page
        router.push({
            name: "ExamOnlineExam",
        })
    }
}

const handlePostSubmissionFlow = () => {
    // For manual submissions, use the existing logic with polling for flexible exams
    if (onlineExam.isFlexibleTiming && onlineExam.autoPublishResultsForFlexibleTiming) {
        // For MCQ exams, results should be available immediately
        if (onlineExam.type?.value === 'mcq') {
            checkForResultsAndRedirect() // Use polling mechanism for manual submissions
        } else {
            // For mixed/essay exams, redirect to main page (results will be published after manual evaluation)
            router.push({
                name: "ExamOnlineExam",
            })
        }
    } else {
        // For traditional exams or manual publish, redirect to main page
        router.push({
            name: "ExamOnlineExam",
        })
    }
}

const checkForResultsAndRedirect = async () => {
    let attempts = 0
    const maxAttempts = 5
    const checkInterval = 500 // 500ms

    const checkResults = async () => {
        try {
            const response = await store.dispatch("exam/onlineExam/get", {
                uuid: route.params.uuid,
                params: {
                    submission: true,
                },
            })

            if (response.resultPublishedAt?.value) {
                // Results are published, redirect to results page
                router.push({
                    name: "ExamOnlineExamStudentSubmission",
                    params: { uuid: route.params.uuid },
                })
                return
            }

            attempts++
            if (attempts < maxAttempts) {
                // Try again after a short delay
                setTimeout(checkResults, checkInterval)
            } else {
                // Max attempts reached, redirect to main page
                router.push({
                    name: "ExamOnlineExam",
                })
            }
        } catch (error) {
            // Error occurred, redirect to main page
            router.push({
                name: "ExamOnlineExam",
            })
        }
    }

    // Start checking
    setTimeout(checkResults, checkInterval)
}

const afterSubmit = (data) => {
    state.submittedAnswerCount = data.submittedAnswerCount
    // getOnlineExam()
}

const getTimeStatus = async () => {
    if (!onlineExam.uuid) return

    try {
        await store
            .dispatch("exam/onlineExam/getTimeStatus", {
                uuid: route.params.uuid,
            })
            .then((response) => {
                timeStatus.value = response

                // Set initial countdown values for flexible exams
                if (onlineExam.isFlexibleTiming && response.started && response.remainingTime !== null) {
                    if (initialRemainingTime.value === null) {
                        initialRemainingTime.value = response.remainingTime
                        countdownStartTime.value = currentTime.value // Use the same time reference
                    }
                }

                // Auto-submit if expired (for both flexible and traditional exams)
                if (response.expired && response.started && !onlineExam.submittedAt?.value) {
                    autoSubmitExam()
                }
            })
    } catch (e) {
        console.error("Failed to get time status:", e)
    }
}

// Proctoring Functions
const initializeProctoring = async () => {
    // Show requirements check modal first
    showRequirementsCheck.value = true
}

const onRequirementsMet = async () => {
    showRequirementsCheck.value = false

    try {
        proctorStatus.errors = []

        // Initialize proctoring composable
        proctoring.value = useProctoring(
            route.params.uuid,
            null, // Will be set when submission starts
            onlineExam.proctorConfig
        )

        // Initialize security composable
        examSecurity.value = useExamSecurity(
            route.params.uuid,
            null, // Will be set when submission starts
            onlineExam.proctorConfig
        )

        // Initialize proctoring system
        await proctoring.value.initialize()

        // Initialize security measures
        examSecurity.value.initialize()

        proctorStatus.isInitialized = true
        proctorStatus.webcamStatus = 'ready'
        proctorStatus.microphoneStatus = 'ready'
        proctorStatus.faceDetectionStatus = 'ready'
        proctorStatus.fullscreenStatus = 'ready'

    } catch (error) {
        console.error('Failed to initialize proctoring:', error)
        proctorStatus.errors.push(error.message)
        proctorStatus.isInitialized = false
    }
}

const startProctoring = async () => {
    if (!proctoring.value || !examSecurity.value) {
        throw new Error('Proctoring not initialized')
    }

    try {
        // Start monitoring
        proctoring.value.startMonitoring()

        proctorStatus.isActive = true
        proctorStatus.webcamStatus = 'active'
        proctorStatus.microphoneStatus = 'active'
        proctorStatus.faceDetectionStatus = 'active'
        proctorStatus.fullscreenStatus = 'active'

        // Update event counts periodically
        setInterval(() => {
            if (proctoring.value) {
                Object.assign(proctorStatus.eventCounts, proctoring.value.eventCounts)
            }
        }, 1000)

    } catch (error) {
        console.error('Failed to start proctoring:', error)
        proctorStatus.errors.push(error.message)
    }
}

const stopProctoring = () => {
    if (proctoring.value) {
        proctoring.value.stopMonitoring()
        proctorStatus.isActive = false
        proctorStatus.webcamStatus = 'inactive'
        proctorStatus.microphoneStatus = 'inactive'
        proctorStatus.faceDetectionStatus = 'inactive'
        proctorStatus.fullscreenStatus = 'inactive'
    }

    if (examSecurity.value) {
        examSecurity.value.cleanup()
    }
}

const getTotalViolations = () => {
    return proctorStatus.eventCounts.tabSwitches +
           proctorStatus.eventCounts.fullscreenExits +
           proctorStatus.eventCounts.copyPasteAttempts +
           proctorStatus.eventCounts.faceDetectionFailures
}

// Proctoring alert management
const addAlert = (type, title, message, actions = [], persistent = false) => {
    const alert = {
        id: Date.now() + Math.random(),
        type,
        title,
        message,
        actions,
        persistent
    }
    proctorAlerts.value.push(alert)
    return alert.id
}

const removeAlert = (id) => {
    const index = proctorAlerts.value.findIndex(alert => alert.id === id)
    if (index > -1) {
        proctorAlerts.value.splice(index, 1)
    }
}

const handleAlertAction = (action) => {
    if (action.callback) {
        action.callback()
    }
}

// Test functions for proctoring
const testCamera = async () => {
    try {
        if (proctoring.value && proctoring.value.videoStream) {
            addAlert('info', 'Camera Test', 'Camera is working properly', [], false)
        } else {
            addAlert('error', 'Camera Test Failed', 'Unable to access camera', [], false)
        }
    } catch (error) {
        addAlert('error', 'Camera Test Failed', error.message, [], false)
    }
}

const testMicrophone = async () => {
    try {
        if (proctoring.value && proctoring.value.audioStream) {
            addAlert('info', 'Microphone Test', 'Microphone is working properly', [], false)
        } else {
            addAlert('error', 'Microphone Test Failed', 'Unable to access microphone', [], false)
        }
    } catch (error) {
        addAlert('error', 'Microphone Test Failed', error.message, [], false)
    }
}

// Watch for client-side expiry and auto-submit
watch(isExamExpired, (expired, wasExpired) => {
    // Only auto-submit if exam just expired (not already expired)
    if (expired && !wasExpired && onlineExam.startedAt?.value && !onlineExam.submittedAt?.value) {
        if (onlineExam.isFlexibleTiming) {
            // For flexible exams, ensure we have valid countdown data
            if (initialRemainingTime.value !== null && countdownStartTime.value !== null) {
                autoSubmitExam()
            }
        } else {
            // For traditional exams, auto-submit when time expires
            autoSubmitExam()
        }
    }
})

onMounted(async () => {
    await getOnlineExam()

    // Only get time status for flexible exams
    if (onlineExam.isFlexibleTiming) {
        await getTimeStatus()
    }

    // Start the timer for client-side countdown only (no backend calls)
    timerInterval.value = setInterval(() => {
        currentTime.value = new Date()
    }, 1000) // Updates every second
})

onUnmounted(() => {
    if (timerInterval.value) {
        clearInterval(timerInterval.value)
    }

    // Cleanup proctoring
    if (onlineExam.enableProctoring) {
        stopProctoring()
    }
})


</script>
