<template>
    <BaseModal :show="visibility" @close="$emit('close')" size="xl">
        <template #title>
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <i class="fas fa-eye"></i>
                    <span>{{ getMediaTitle() }}</span>
                </div>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-500">
                        {{ formatDateTime(media.event.detectedAt.formatted) }}
                    </span>
                    <BaseButton
                        size="sm"
                        design="secondary"
                        @click="downloadMedia"
                    >
                        <i class="fas fa-download mr-1"></i>
                        {{ $trans("general.download") || "Download" }}
                    </BaseButton>
                </div>
            </div>
        </template>

        <div class="space-y-4">
            <!-- Event Context -->
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                        <span class="font-medium text-gray-700 dark:text-gray-300">Event Type:</span>
                        <div>{{ getEventTitle(media.event.eventType) }}</div>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700 dark:text-gray-300">Severity:</span>
                        <div>
                            <span
                                class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium"
                                :class="getSeverityClass(media.event.severity)"
                            >
                                {{ media.event.severity }}
                            </span>
                        </div>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700 dark:text-gray-300">Time:</span>
                        <div>{{ formatTime(media.event.detectedAt.formatted) }}</div>
                    </div>
                </div>
                <div v-if="media.event.description" class="mt-2">
                    <span class="font-medium text-gray-700 dark:text-gray-300">Description:</span>
                    <div class="text-sm">{{ media.event.formattedDescription || media.event.description }}</div>
                </div>
            </div>

            <!-- Media Content -->
            <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <!-- Image Viewer -->
                <div v-if="isImage" class="p-4">
                    <div class="text-center">
                        <img 
                            :src="media.url" 
                            :alt="getMediaTitle()"
                            class="max-w-full h-auto rounded-lg shadow-lg mx-auto"
                            style="max-height: 70vh;"
                            @load="onImageLoad"
                            @error="onMediaError"
                        />
                        
                        <!-- Image Controls -->
                        <div class="mt-4 flex justify-center space-x-2">
                            <BaseButton
                                size="sm"
                                design="secondary"
                                @click="zoomIn"
                                :disabled="zoomLevel >= 3"
                            >
                                <i class="fas fa-search-plus mr-1"></i>
                                {{ $trans("general.zoom_in") || "Zoom In" }}
                            </BaseButton>
                            <BaseButton
                                size="sm"
                                design="secondary"
                                @click="zoomOut"
                                :disabled="zoomLevel <= 0.5"
                            >
                                <i class="fas fa-search-minus mr-1"></i>
                                {{ $trans("general.zoom_out") || "Zoom Out" }}
                            </BaseButton>
                            <BaseButton
                                size="sm"
                                design="secondary"
                                @click="resetZoom"
                            >
                                <i class="fas fa-expand-arrows-alt mr-1"></i>
                                {{ $trans("general.reset") || "Reset" }}
                            </BaseButton>
                        </div>
                        
                        <!-- Image Info -->
                        <div v-if="imageInfo" class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                            {{ imageInfo.width }} × {{ imageInfo.height }} pixels
                        </div>
                    </div>
                </div>

                <!-- Audio Player -->
                <div v-else-if="isAudio" class="p-6">
                    <div class="text-center space-y-4">
                        <div class="text-6xl text-gray-400">
                            <i class="fas fa-volume-up"></i>
                        </div>
                        
                        <audio 
                            ref="audioPlayer"
                            controls 
                            class="w-full max-w-md mx-auto"
                            @loadedmetadata="onAudioLoad"
                            @error="onMediaError"
                        >
                            <source :src="media.url" type="audio/wav">
                            <source :src="media.url" type="audio/mp3">
                            <source :src="media.url" type="audio/ogg">
                            Your browser does not support the audio element.
                        </audio>
                        
                        <!-- Audio Info -->
                        <div v-if="audioInfo" class="text-sm text-gray-600 dark:text-gray-400">
                            Duration: {{ formatDuration(audioInfo.duration) }}
                        </div>
                        
                        <!-- Audio Level Visualization -->
                        <div v-if="media.event.data && media.event.data.audioLevel !== undefined" class="space-y-2">
                            <div class="text-sm font-medium">Audio Level: {{ media.event.data.audioLevel }}dB</div>
                            <div class="w-full bg-gray-200 rounded-full h-3">
                                <div 
                                    class="h-3 rounded-full transition-all duration-300"
                                    :class="getAudioLevelColor(media.event.data.audioLevel)"
                                    :style="{ width: getAudioLevelPercentage(media.event.data.audioLevel) + '%' }"
                                ></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Video Player -->
                <div v-else-if="isVideo" class="p-4">
                    <div class="text-center">
                        <video 
                            ref="videoPlayer"
                            controls 
                            class="max-w-full h-auto rounded-lg shadow-lg mx-auto"
                            style="max-height: 70vh;"
                            @loadedmetadata="onVideoLoad"
                            @error="onMediaError"
                        >
                            <source :src="media.url" type="video/mp4">
                            <source :src="media.url" type="video/webm">
                            Your browser does not support the video element.
                        </video>
                        
                        <!-- Video Info -->
                        <div v-if="videoInfo" class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                            {{ videoInfo.width }} × {{ videoInfo.height }} pixels, 
                            Duration: {{ formatDuration(videoInfo.duration) }}
                        </div>
                    </div>
                </div>

                <!-- Unsupported Media -->
                <div v-else class="p-6 text-center">
                    <div class="text-6xl text-gray-400 mb-4">
                        <i class="fas fa-file"></i>
                    </div>
                    <p class="text-gray-600 dark:text-gray-400 mb-4">
                        {{ $trans("exam.proctoring.review.unsupported_media") || "Media type not supported for preview" }}
                    </p>
                    <BaseButton design="primary" @click="downloadMedia">
                        <i class="fas fa-download mr-1"></i>
                        {{ $trans("general.download") || "Download" }}
                    </BaseButton>
                </div>
            </div>

            <!-- Error State -->
            <div v-if="hasError" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                    <span class="text-red-700 dark:text-red-300">
                        {{ $trans("exam.proctoring.review.media_load_error") || "Failed to load media file" }}
                    </span>
                </div>
            </div>
        </div>

        <div class="flex justify-between p-4">
            <div class="flex space-x-2">
                <BaseButton design="secondary" @click="openInNewTab">
                    <i class="fas fa-external-link-alt mr-1"></i>
                    {{ $trans("general.open_in_new_tab") || "Open in New Tab" }}
                </BaseButton>
            </div>
            <div class="flex space-x-2">
                <BaseButton design="secondary" @click="$emit('close')">
                    {{ $trans("general.close") || "Close" }}
                </BaseButton>
            </div>
        </div>
    </BaseModal>
</template>

<script>
export default {
    name: "ProctorMediaViewer",
}
</script>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'

defineEmits(['close'])

const props = defineProps({
    media: {
        type: Object,
        required: true
    },
    visibility: {
        type: Boolean,
        default: false
    }
})

// State
const hasError = ref(false)
const zoomLevel = ref(1)
const imageInfo = ref(null)
const audioInfo = ref(null)
const videoInfo = ref(null)

// Refs
const audioPlayer = ref(null)
const videoPlayer = ref(null)

// Computed
const isImage = computed(() => {
    return ['webcam_capture', 'screen_capture'].includes(props.media.event.eventType) ||
           props.media.url.match(/\.(jpg|jpeg|png|gif|webp)$/i)
})

const isAudio = computed(() => {
    return ['audio_alert', 'audio_recording'].includes(props.media.event.eventType) ||
           props.media.url.match(/\.(mp3|wav|ogg|m4a)$/i)
})

const isVideo = computed(() => {
    return ['screen_recording', 'video_recording'].includes(props.media.event.eventType) ||
           props.media.url.match(/\.(mp4|webm|avi|mov)$/i)
})

// Methods
const getMediaTitle = () => {
    const eventTitle = getEventTitle(props.media.event.eventType)
    return `${eventTitle} - Media`
}

const getEventTitle = (eventType) => {
    const titleMap = {
        webcam_capture: 'Webcam Capture',
        audio_alert: 'Audio Alert',
        screen_recording: 'Screen Recording',
        video_recording: 'Video Recording'
    }
    
    return titleMap[eventType] || eventType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const getSeverityClass = (severity) => {
    switch (severity) {
        case 'critical':
            return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
        case 'warning':
            return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
        case 'info':
            return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
        default:
            return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
}

const formatDateTime = (timestamp) => {
    if (!timestamp) return 'N/A'
    
    const date = new Date(timestamp)
    return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    })
}

const formatTime = (timestamp) => {
    if (!timestamp) return 'N/A'
    
    const date = new Date(timestamp)
    return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    })
}

const formatDuration = (seconds) => {
    if (!seconds) return '0:00'
    
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

const onImageLoad = (event) => {
    hasError.value = false
    imageInfo.value = {
        width: event.target.naturalWidth,
        height: event.target.naturalHeight
    }
}

const onAudioLoad = () => {
    hasError.value = false
    if (audioPlayer.value) {
        audioInfo.value = {
            duration: audioPlayer.value.duration
        }
    }
}

const onVideoLoad = () => {
    hasError.value = false
    if (videoPlayer.value) {
        videoInfo.value = {
            width: videoPlayer.value.videoWidth,
            height: videoPlayer.value.videoHeight,
            duration: videoPlayer.value.duration
        }
    }
}

const onMediaError = () => {
    hasError.value = true
}

const zoomIn = () => {
    if (zoomLevel.value < 3) {
        zoomLevel.value += 0.25
    }
}

const zoomOut = () => {
    if (zoomLevel.value > 0.5) {
        zoomLevel.value -= 0.25
    }
}

const resetZoom = () => {
    zoomLevel.value = 1
}

const getAudioLevelColor = (level) => {
    if (level > -20) return 'bg-red-500'
    if (level > -40) return 'bg-yellow-500'
    return 'bg-green-500'
}

const getAudioLevelPercentage = (level) => {
    const minDb = -80
    const maxDb = 0
    const percentage = ((level - minDb) / (maxDb - minDb)) * 100
    return Math.max(0, Math.min(100, percentage))
}

const downloadMedia = () => {
    const link = document.createElement('a')
    link.href = props.media.url
    link.download = `${props.media.event.eventType}_${props.media.event.uuid}`
    link.click()
}

const openInNewTab = () => {
    window.open(props.media.url, '_blank')
}

// Watch for visibility changes to reset state
watch(() => props.visibility, (newValue) => {
    if (newValue) {
        hasError.value = false
        zoomLevel.value = 1
        imageInfo.value = null
        audioInfo.value = null
        videoInfo.value = null
    }
})
</script>
