# Proctoring System API Documentation

## Authentication

All API endpoints require authentication using Laravel Sanctum tokens or session-based authentication.

```http
Authorization: Bearer {token}
```

## Base URL

```
https://your-domain.com/api
```

## Endpoints

### 1. Store Proctoring Event

Creates a new proctoring log entry for an exam session.

**Endpoint:** `POST /online-exams/{examUuid}/proctoring/logs`

**Parameters:**
- `examUuid` (string, required): UUID of the online exam

**Request Body:**
```json
{
    "eventType": "webcam_capture",
    "severity": "info",
    "description": "Webcam image captured successfully",
    "data": {
        "face_count": 1,
        "confidence": 0.95,
        "faces": [
            {
                "x": 100,
                "y": 150,
                "width": 200,
                "height": 250,
                "confidence": 0.95
            }
        ]
    },
    "detectedAt": "2024-01-15T10:30:00Z",
    "ipAddress": "***********",
    "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
}
```

**Request Body (with Media):**
```http
Content-Type: multipart/form-data

eventType: webcam_capture
severity: info
description: Webcam image captured
data: {"face_count": 1}
detectedAt: 2024-01-15T10:30:00Z
media: [binary file data]
```

**Response (201 Created):**
```json
{
    "uuid": "550e8400-e29b-41d4-a716-************",
    "event_type": "webcam_capture",
    "severity": "info",
    "description": "Webcam image captured successfully",
    "data": {
        "face_count": 1,
        "confidence": 0.95
    },
    "detected_at": "2024-01-15T10:30:00Z",
    "has_media": true,
    "media_url": "https://your-domain.com/storage/proctoring/...",
    "created_at": "2024-01-15T10:30:00Z"
}
```

**Validation Rules:**
- `eventType`: required, must be one of: `webcam_capture`, `audio_alert`, `tab_switch`, `fullscreen_exit`, `copy_paste_attempt`, `face_detection_failure`, `suspicious_activity`
- `severity`: required, must be one of: `info`, `warning`, `critical`
- `description`: required, string, max 1000 characters
- `data`: optional, object
- `detectedAt`: required, valid ISO 8601 datetime
- `ipAddress`: optional, valid IP address
- `userAgent`: optional, string, max 1000 characters
- `media`: optional, file (image: jpg,jpeg,png,gif | audio: mp3,wav,ogg | video: mp4,webm)

### 2. Get Proctoring Logs

Retrieves proctoring logs for an exam with filtering and pagination.

**Endpoint:** `GET /online-exams/{examUuid}/proctoring/logs`

**Parameters:**
- `examUuid` (string, required): UUID of the online exam

**Query Parameters:**
- `event_type` (string, optional): Filter by event type
- `severity` (string, optional): Filter by severity level
- `student_id` (string, optional): Filter by student UUID
- `start_date` (string, optional): Start date filter (YYYY-MM-DD)
- `end_date` (string, optional): End date filter (YYYY-MM-DD)
- `per_page` (integer, optional): Results per page (default: 15, max: 100)
- `page` (integer, optional): Page number (default: 1)
- `export` (boolean, optional): Export as CSV instead of JSON

**Response (200 OK):**
```json
{
    "data": [
        {
            "uuid": "550e8400-e29b-41d4-a716-************",
            "event_type": "webcam_capture",
            "severity": "info",
            "description": "Webcam image captured",
            "data": {
                "face_count": 1,
                "confidence": 0.95
            },
            "detected_at": "2024-01-15T10:30:00Z",
            "has_media": true,
            "media_url": "https://your-domain.com/storage/proctoring/...",
            "student": {
                "uuid": "student-uuid",
                "name": "John Doe",
                "admission_number": "STU001"
            },
            "ip_address": "***********",
            "created_at": "2024-01-15T10:30:00Z"
        }
    ],
    "meta": {
        "current_page": 1,
        "from": 1,
        "last_page": 5,
        "per_page": 15,
        "to": 15,
        "total": 75
    },
    "links": {
        "first": "https://your-domain.com/api/online-exams/{examUuid}/proctoring/logs?page=1",
        "last": "https://your-domain.com/api/online-exams/{examUuid}/proctoring/logs?page=5",
        "prev": null,
        "next": "https://your-domain.com/api/online-exams/{examUuid}/proctoring/logs?page=2"
    }
}
```

**CSV Export Response:**
```csv
Event Type,Severity,Description,Detected At,Student Name,IP Address,Has Media
webcam_capture,info,Webcam image captured,2024-01-15 10:30:00,John Doe,***********,Yes
audio_alert,warning,Audio threshold exceeded,2024-01-15 10:35:00,John Doe,***********,No
```

### 3. Get Submission Proctoring Summary

Retrieves a summary of proctoring events for a specific exam submission.

**Endpoint:** `GET /online-exams/{examUuid}/proctoring/submissions/{submissionUuid}/summary`

**Parameters:**
- `examUuid` (string, required): UUID of the online exam
- `submissionUuid` (string, required): UUID of the exam submission

**Response (200 OK):**
```json
{
    "summary": {
        "total_events": 25,
        "critical_events": 2,
        "warning_events": 8,
        "info_events": 15,
        "event_types": {
            "webcam_capture": 15,
            "audio_alert": 5,
            "tab_switch": 3,
            "face_detection_failure": 2
        },
        "timeline": [
            {
                "time": "10:30:00",
                "event_type": "webcam_capture",
                "severity": "info",
                "description": "Session started",
                "has_media": false
            },
            {
                "time": "10:35:00",
                "event_type": "audio_alert",
                "severity": "warning",
                "description": "Audio threshold exceeded",
                "has_media": true
            }
        ],
        "session_duration": "02:30:00",
        "violations_per_hour": 3.2,
        "overall_status": "requires_review"
    }
}
```

### 4. Get Submission Proctoring Logs

Retrieves detailed proctoring logs for a specific submission.

**Endpoint:** `GET /online-exams/{examUuid}/proctoring/submissions/{submissionUuid}/logs`

**Parameters:**
- `examUuid` (string, required): UUID of the online exam
- `submissionUuid` (string, required): UUID of the exam submission

**Query Parameters:** Same as endpoint #2

**Response:** Same format as endpoint #2, but filtered to the specific submission

### 5. Validate Proctoring Requirements

Validates that a student's system meets the proctoring requirements for an exam.

**Endpoint:** `POST /online-exams/{examUuid}/proctoring/validate-requirements`

**Parameters:**
- `examUuid` (string, required): UUID of the online exam

**Request Body:**
```json
{
    "webcamAvailable": true,
    "microphoneAvailable": true,
    "screenRecordingAvailable": true,
    "browserCompatible": true,
    "systemInfo": {
        "browser": "Chrome",
        "version": "120.0.0.0",
        "os": "Windows 10",
        "screen_resolution": "1920x1080"
    }
}
```

**Response (200 OK):**
```json
{
    "valid": true,
    "requirements_met": [
        "webcam_available",
        "microphone_available",
        "screen_recording_available",
        "browser_compatible"
    ],
    "requirements_failed": [],
    "warnings": [],
    "session_token": "proctoring-session-token-123"
}
```

**Response (422 Validation Failed):**
```json
{
    "valid": false,
    "requirements_met": [
        "webcam_available",
        "browser_compatible"
    ],
    "requirements_failed": [
        "microphone_available",
        "screen_recording_available"
    ],
    "warnings": [
        "Browser version is outdated, some features may not work properly"
    ],
    "errors": [
        "Microphone access is required for this exam",
        "Screen recording capability is required for this exam"
    ]
}
```

### 6. Get Exam Proctoring Configuration

Retrieves the proctoring configuration for an exam.

**Endpoint:** `GET /online-exams/{examUuid}/proctoring/config`

**Parameters:**
- `examUuid` (string, required): UUID of the online exam

**Response (200 OK):**
```json
{
    "enable_proctoring": true,
    "proctor_config": {
        "webcam_monitoring": true,
        "microphone_monitoring": true,
        "screen_recording": true,
        "fullscreen_enforcement": true,
        "copy_paste_blocking": true,
        "face_detection": true,
        "capture_interval_seconds": 30,
        "audio_threshold_db": -40,
        "max_face_detection_failures": 5,
        "allow_tab_switching": false,
        "auto_submit_on_violations": false,
        "custom_instructions": "Please ensure you are in a well-lit room."
    },
    "requirements": {
        "webcam_required": true,
        "microphone_required": true,
        "screen_recording_required": true,
        "fullscreen_required": true
    }
}
```

## Error Responses

### 400 Bad Request
```json
{
    "message": "Invalid request data",
    "errors": {
        "eventType": ["The event type field is required."],
        "severity": ["The severity field must be one of: info, warning, critical."]
    }
}
```

### 401 Unauthorized
```json
{
    "message": "Unauthenticated"
}
```

### 403 Forbidden
```json
{
    "message": "Proctoring is not enabled for this exam"
}
```

### 404 Not Found
```json
{
    "message": "Exam not found"
}
```

### 422 Validation Error
```json
{
    "message": "The given data was invalid",
    "errors": {
        "detectedAt": ["The detected at field must be a valid date."],
        "media": ["The media field must be a file of type: jpg, jpeg, png, gif, mp3, wav, ogg, mp4, webm."]
    }
}
```

### 429 Too Many Requests
```json
{
    "message": "Too many requests. Please try again later.",
    "retry_after": 60
}
```

### 500 Internal Server Error
```json
{
    "message": "Internal server error",
    "error_id": "550e8400-e29b-41d4-a716-************"
}
```

## Rate Limiting

API endpoints are rate limited to prevent abuse:
- **Proctoring log creation**: 60 requests per minute per user
- **Data retrieval**: 100 requests per minute per user
- **File uploads**: 10 requests per minute per user

## File Upload Limits

- **Maximum file size**: 10MB per file
- **Supported image formats**: JPG, JPEG, PNG, GIF
- **Supported audio formats**: MP3, WAV, OGG
- **Supported video formats**: MP4, WEBM

## Security Considerations

1. **HTTPS Required**: All API calls must use HTTPS
2. **Authentication**: Valid authentication token required
3. **Authorization**: User must have appropriate permissions
4. **Input Validation**: All input is validated and sanitized
5. **File Scanning**: Uploaded files are scanned for malware
6. **Rate Limiting**: Prevents abuse and DoS attacks

## SDK and Libraries

### JavaScript SDK Example
```javascript
class ProctorAPI {
    constructor(baseUrl, token) {
        this.baseUrl = baseUrl;
        this.token = token;
    }

    async logEvent(examUuid, eventData) {
        const response = await fetch(`${this.baseUrl}/online-exams/${examUuid}/proctoring/logs`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.token}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(eventData)
        });
        return response.json();
    }

    async uploadMedia(examUuid, eventData, mediaFile) {
        const formData = new FormData();
        Object.keys(eventData).forEach(key => {
            formData.append(key, typeof eventData[key] === 'object' ? JSON.stringify(eventData[key]) : eventData[key]);
        });
        formData.append('media', mediaFile);

        const response = await fetch(`${this.baseUrl}/online-exams/${examUuid}/proctoring/logs`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.token}`,
            },
            body: formData
        });
        return response.json();
    }
}
```

### Usage Example
```javascript
const proctorAPI = new ProctorAPI('https://your-domain.com/api', 'your-auth-token');

// Log a webcam capture event
await proctorAPI.logEvent('exam-uuid', {
    eventType: 'webcam_capture',
    severity: 'info',
    description: 'Webcam image captured',
    data: { face_count: 1 },
    detectedAt: new Date().toISOString()
});

// Upload media with event
const mediaFile = new File([blob], 'webcam.jpg', { type: 'image/jpeg' });
await proctorAPI.uploadMedia('exam-uuid', eventData, mediaFile);
```
