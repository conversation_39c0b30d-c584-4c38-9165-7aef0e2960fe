<?php

use App\Models\Exam\OnlineExam;
use App\Models\Team;

beforeEach(function () {
    $this->team = Team::factory()->create();
});

it('can enable proctoring', function () {
    $exam = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => [
            'webcam_monitoring' => true,
            'microphone_monitoring' => true,
            'screen_recording' => true,
            'fullscreen_enforcement' => true,
            'copy_paste_blocking' => true,
            'face_detection' => true,
            'capture_interval_seconds' => 30,
            'audio_threshold_db' => -40,
            'max_face_detection_failures' => 5,
            'allow_tab_switching' => false,
            'auto_submit_on_violations' => false,
            'custom_instructions' => 'Please ensure good lighting for webcam.',
        ],
    ]);

    expect($exam->enable_proctoring)->toBeTrue();
    expect($exam->proctor_config)->toBeArray();
    expect($exam->proctor_config['webcam_monitoring'])->toBeTrue();
    expect($exam->proctor_config['capture_interval_seconds'])->toBe(30);
});

it('can disable proctoring', function () {
    $exam = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => false,
        'proctor_config' => [],
    ]);

    expect($exam->enable_proctoring)->toBeFalse();
    expect($exam->proctor_config)->toBeArray();
    expect($exam->proctor_config)->toBeEmpty();
});

it('has default proctor config when proctoring is disabled', function () {
    $exam = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => false,
    ]);

    expect($exam->proctor_config)->toBeArray();
});

it('can check if webcam monitoring is enabled', function () {
    $examWithWebcam = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => ['webcam_monitoring' => true],
    ]);

    $examWithoutWebcam = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => ['webcam_monitoring' => false],
    ]);

    expect($examWithWebcam->hasWebcamMonitoring())->toBeTrue();
    expect($examWithoutWebcam->hasWebcamMonitoring())->toBeFalse();
});

it('can check if microphone monitoring is enabled', function () {
    $examWithMicrophone = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => ['microphone_monitoring' => true],
    ]);

    $examWithoutMicrophone = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => ['microphone_monitoring' => false],
    ]);

    expect($examWithMicrophone->hasMicrophoneMonitoring())->toBeTrue();
    expect($examWithoutMicrophone->hasMicrophoneMonitoring())->toBeFalse();
});

it('can check if face detection is enabled', function () {
    $examWithFaceDetection = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => ['face_detection' => true],
    ]);

    $examWithoutFaceDetection = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => ['face_detection' => false],
    ]);

    expect($examWithFaceDetection->hasFaceDetection())->toBeTrue();
    expect($examWithoutFaceDetection->hasFaceDetection())->toBeFalse();
});

it('can check if screen recording is enabled', function () {
    $examWithScreenRecording = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => ['screen_recording' => true],
    ]);

    $examWithoutScreenRecording = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => ['screen_recording' => false],
    ]);

    expect($examWithScreenRecording->hasScreenRecording())->toBeTrue();
    expect($examWithoutScreenRecording->hasScreenRecording())->toBeFalse();
});

it('can check if fullscreen enforcement is enabled', function () {
    $examWithFullscreen = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => ['fullscreen_enforcement' => true],
    ]);

    $examWithoutFullscreen = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => ['fullscreen_enforcement' => false],
    ]);

    expect($examWithFullscreen->hasFullscreenEnforcement())->toBeTrue();
    expect($examWithoutFullscreen->hasFullscreenEnforcement())->toBeFalse();
});

it('can check if copy paste blocking is enabled', function () {
    $examWithCopyPasteBlocking = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => ['copy_paste_blocking' => true],
    ]);

    $examWithoutCopyPasteBlocking = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => ['copy_paste_blocking' => false],
    ]);

    expect($examWithCopyPasteBlocking->hasCopyPasteBlocking())->toBeTrue();
    expect($examWithoutCopyPasteBlocking->hasCopyPasteBlocking())->toBeFalse();
});

it('can get capture interval', function () {
    $exam = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => ['capture_interval_seconds' => 45],
    ]);

    expect($exam->getCaptureInterval())->toBe(45);
});

it('returns default capture interval when not set', function () {
    $exam = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => [],
    ]);

    expect($exam->getCaptureInterval())->toBe(30); // Default value
});

it('can get audio threshold', function () {
    $exam = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => ['audio_threshold_db' => -50],
    ]);

    expect($exam->getAudioThreshold())->toBe(-50);
});

it('returns default audio threshold when not set', function () {
    $exam = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => [],
    ]);

    expect($exam->getAudioThreshold())->toBe(-40); // Default value
});

it('can get max face detection failures', function () {
    $exam = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => ['max_face_detection_failures' => 3],
    ]);

    expect($exam->getMaxFaceDetectionFailures())->toBe(3);
});

it('returns default max face detection failures when not set', function () {
    $exam = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => [],
    ]);

    expect($exam->getMaxFaceDetectionFailures())->toBe(5); // Default value
});

it('can check if tab switching is allowed', function () {
    $examAllowingTabSwitch = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => ['allow_tab_switching' => true],
    ]);

    $examNotAllowingTabSwitch = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => ['allow_tab_switching' => false],
    ]);

    expect($examAllowingTabSwitch->allowsTabSwitching())->toBeTrue();
    expect($examNotAllowingTabSwitch->allowsTabSwitching())->toBeFalse();
});

it('can check if auto submit on violations is enabled', function () {
    $examWithAutoSubmit = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => ['auto_submit_on_violations' => true],
    ]);

    $examWithoutAutoSubmit = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => ['auto_submit_on_violations' => false],
    ]);

    expect($examWithAutoSubmit->hasAutoSubmitOnViolations())->toBeTrue();
    expect($examWithoutAutoSubmit->hasAutoSubmitOnViolations())->toBeFalse();
});

it('can get custom instructions', function () {
    $instructions = 'Please ensure you are in a well-lit room.';
    
    $exam = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => ['custom_instructions' => $instructions],
    ]);

    expect($exam->getCustomInstructions())->toBe($instructions);
});

it('returns empty string for custom instructions when not set', function () {
    $exam = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => [],
    ]);

    expect($exam->getCustomInstructions())->toBe('');
});

it('can get proctoring summary', function () {
    $exam = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => [
            'webcam_monitoring' => true,
            'microphone_monitoring' => true,
            'face_detection' => true,
            'screen_recording' => false,
            'fullscreen_enforcement' => true,
            'copy_paste_blocking' => true,
        ],
    ]);

    $summary = $exam->getProctoringSummary();

    expect($summary)->toBeArray();
    expect($summary['enabled_features'])->toContain('webcam_monitoring');
    expect($summary['enabled_features'])->toContain('microphone_monitoring');
    expect($summary['enabled_features'])->toContain('face_detection');
    expect($summary['enabled_features'])->not->toContain('screen_recording');
    expect($summary['total_features'])->toBeGreaterThan(0);
});

it('returns empty summary when proctoring is disabled', function () {
    $exam = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => false,
    ]);

    $summary = $exam->getProctoringSummary();

    expect($summary)->toBeArray();
    expect($summary['enabled_features'])->toBeEmpty();
    expect($summary['total_features'])->toBe(0);
});
