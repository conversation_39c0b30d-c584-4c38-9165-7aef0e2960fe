# Online Exam Proctoring System Documentation

## Table of Contents
1. [Overview](#overview)
2. [System Architecture](#system-architecture)
3. [Installation & Setup](#installation--setup)
4. [Configuration](#configuration)
5. [Administrator Guide](#administrator-guide)
6. [Examiner Guide](#examiner-guide)
7. [Student Experience](#student-experience)
8. [API Reference](#api-reference)
9. [Security & Privacy](#security--privacy)
10. [Troubleshooting](#troubleshooting)

## Overview

The Online Exam Proctoring System provides comprehensive monitoring and security features for online examinations. It includes webcam monitoring, audio detection, screen recording, face detection, and various security measures to maintain academic integrity.

### Key Features
- **Webcam Monitoring**: Periodic image capture from student webcams
- **Audio Detection**: Real-time audio level monitoring and alerts
- **Face Detection**: AI-powered face detection and identity verification
- **Screen Recording**: Full screen activity recording and tab switching detection
- **Security Measures**: Fullscreen enforcement, copy-paste blocking, and violation handling
- **Comprehensive Logging**: Detailed event logging with severity levels
- **Review Dashboard**: Examiner interface for reviewing proctoring data
- **Flexible Configuration**: Per-exam proctoring settings with preset security levels

## System Architecture

### Backend Components
```
app/
├── Models/
│   └── Exam/
│       ├── OnlineExam.php (Enhanced with proctoring)
│       └── ProctorLog.php (New proctoring log model)
├── Http/
│   ├── Controllers/
│   │   └── Exam/
│   │       └── OnlineExamProctorController.php
│   └── Requests/
│       └── Exam/
│           └── ProctorLogRequest.php
├── Services/
│   └── Exam/
│       └── ProctorLogService.php
└── Enums/
    └── Exam/
        ├── ProctorEventType.php
        └── ProctorSeverity.php
```

### Frontend Components
```
resources/js/
├── core/
│   └── components/
│       └── Exam/
│           ├── ProctorConfigurationPanel.vue
│           ├── ProctorPresetSelector.vue
│           ├── ProctorRequirementsCheck.vue
│           ├── ProctorReviewDashboard.vue
│           ├── ProctorEventTimeline.vue
│           ├── ProctorEventDetailModal.vue
│           ├── ProctorMediaViewer.vue
│           └── ProctorSummaryWidget.vue
└── views/
    └── Pages/
        └── Exam/
            ├── OnlineExam/
            │   ├── Form.vue (Enhanced with proctoring config)
            │   └── Submission/
            │       ├── Detail.vue (Enhanced with proctoring review)
            │       └── ProctorReview.vue
            └── Config/
                └── Proctoring.vue
```

### Database Schema
```sql
-- Enhanced online_exams table
ALTER TABLE online_exams ADD COLUMN enable_proctoring BOOLEAN DEFAULT FALSE;
ALTER TABLE online_exams ADD COLUMN proctor_config JSON;

-- New proctor_logs table
CREATE TABLE proctor_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    uuid CHAR(36) UNIQUE,
    online_exam_id BIGINT,
    online_exam_record_id BIGINT,
    student_id BIGINT,
    event_type ENUM(...),
    severity ENUM('info', 'warning', 'critical'),
    description TEXT,
    data JSON,
    detected_at TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    media_path VARCHAR(255),
    meta JSON,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

## Installation & Setup

### Prerequisites
- PHP 8.1+
- Laravel 10+
- MySQL 8.0+
- Node.js 18+
- Modern web browser with WebRTC support

### Backend Setup

1. **Run Database Migrations**
```bash
php artisan migrate
```

2. **Seed Permissions**
```bash
php artisan db:seed --class=ProctorPermissionSeeder
```

3. **Configure Storage**
```bash
php artisan storage:link
```

4. **Set Environment Variables**
```env
# Proctoring Configuration
PROCTORING_ENABLED=true
PROCTORING_MEDIA_RETENTION_DAYS=90
PROCTORING_LOG_RETENTION_DAYS=365
PROCTORING_AUTO_CLEANUP=true
```

### Frontend Setup

1. **Install Dependencies**
```bash
npm install
```

2. **Build Assets**
```bash
npm run build
```

### Permissions Setup

The system requires the following permissions:
- `online-exam:manage` - Create and manage exams with proctoring
- `online-exam-proctoring:review` - Review proctoring logs and data
- `online-exam-proctoring:configure` - Configure global proctoring settings

## Configuration

### Global Proctoring Settings

Access the global proctoring configuration at: **Exam > Configuration > Proctoring**

#### System Requirements
- **Browser Requirements**: Modern browser enforcement, HTTPS requirement, incognito mode blocking
- **Hardware Requirements**: Webcam, microphone, and fullscreen capabilities
- **Storage & Retention**: Media and log retention periods, automatic cleanup

#### Privacy & Compliance
- **Privacy Consent**: Require explicit student consent
- **Data Rights**: Allow data export and deletion requests
- **Notification Settings**: Real-time violation alerts and completion notifications

### Per-Exam Configuration

When creating or editing an exam, configure proctoring in the "Proctoring Configuration" section:

#### Quick Setup Presets
- **Disabled**: No proctoring features
- **Low Security**: Basic webcam monitoring, copy-paste blocking
- **Medium Security**: Webcam, audio, face detection, fullscreen enforcement
- **High Security**: All features enabled with strict settings
- **Custom**: Manual configuration of all features

#### Monitoring Features
- **Webcam Monitoring**: Capture interval (10-300 seconds)
- **Microphone Monitoring**: Audio threshold (-80 to 0 dB)
- **Face Detection**: Maximum detection failures (1-20)
- **Screen Recording**: Full screen activity capture

#### Security Features
- **Fullscreen Enforcement**: Force fullscreen mode
- **Copy-Paste Blocking**: Block clipboard operations
- **Tab Switching Control**: Allow or prevent tab changes
- **Auto-Submit**: Automatic submission on critical violations

## Administrator Guide

### Setting Up Proctoring for an Exam

1. **Navigate to Exam Management**
   - Go to Exam > Online Exam
   - Click "Create" or edit an existing exam

2. **Configure Proctoring**
   - Scroll to "Proctoring Configuration" section
   - Toggle "Enable Proctoring"
   - Choose a security preset or configure manually
   - Set monitoring intervals and thresholds
   - Add custom instructions for students

3. **Review Configuration**
   - Check the configuration preview
   - Ensure all required features are enabled
   - Save the exam

### Managing Global Settings

1. **Access Global Configuration**
   - Go to Exam > Configuration > Proctoring
   - Configure system-wide settings

2. **Set Default Configurations**
   - Define default proctoring settings for new exams
   - Configure system requirements
   - Set up privacy and compliance settings

3. **Monitor System Health**
   - Review notification settings
   - Configure data retention policies
   - Set up automatic cleanup schedules

### User Permissions

Assign appropriate permissions to users:
- **Exam Creators**: `online-exam:manage`
- **Examiners**: `online-exam-proctoring:review`
- **Administrators**: All proctoring permissions

## Examiner Guide

### Reviewing Proctoring Data

1. **Access Submission Review**
   - Go to Exam > Online Exam > [Exam Name]
   - Click on a student submission
   - Switch to "Proctoring Review" tab

2. **Review Dashboard Features**
   - **Summary Statistics**: Total events, critical/warning/info counts
   - **Event Timeline**: Chronological view of all proctoring events
   - **Filtering Options**: Filter by event type, severity, time range
   - **Media Viewer**: View captured images and audio recordings

3. **Analyzing Events**
   - **Critical Events**: Face detection failures, multiple violations
   - **Warning Events**: Tab switches, audio alerts, fullscreen exits
   - **Info Events**: Regular webcam captures, session start/end

### Dedicated Proctoring Review Page

For comprehensive review, use the dedicated proctoring review page:
- Access via the "Proctoring Review" action in submission list
- Full-page interface with detailed analysis tools
- Export capabilities for external review

### Event Types and Meanings

| Event Type | Severity | Description |
|------------|----------|-------------|
| `webcam_capture` | Info | Regular webcam image capture |
| `audio_alert` | Warning | Audio level exceeded threshold |
| `tab_switch` | Warning | Student switched browser tabs |
| `fullscreen_exit` | Warning | Student exited fullscreen mode |
| `copy_paste_attempt` | Warning | Attempted copy/paste operation |
| `face_detection_failure` | Critical | No face detected or multiple faces |
| `suspicious_activity` | Critical | Multiple violations in short time |

### Making Decisions

Based on proctoring data:
1. **Clean Sessions**: No critical events, minimal warnings
2. **Minor Issues**: Few warning events, no critical violations
3. **Requires Review**: Multiple critical events or suspicious patterns
4. **Academic Integrity Violation**: Clear evidence of cheating

## Student Experience

### Pre-Exam Requirements Check

Before starting a proctored exam, students must:
1. **Grant Permissions**: Allow webcam, microphone, and screen recording access
2. **Browser Compatibility**: Ensure browser supports all required features
3. **Environment Setup**: Follow any custom instructions provided
4. **Privacy Consent**: Acknowledge proctoring data collection

### During the Exam

Students will experience:
- **Fullscreen Mode**: Exam runs in fullscreen (if enabled)
- **Periodic Captures**: Webcam images taken at configured intervals
- **Audio Monitoring**: Continuous audio level monitoring
- **Restriction Enforcement**: Copy-paste blocking, tab switching prevention
- **Real-time Feedback**: Immediate alerts for violations

### Violation Handling

When violations are detected:
- **Warning Messages**: Immediate feedback for minor violations
- **Escalating Responses**: Increasing severity for repeated violations
- **Auto-Submission**: Automatic exam submission for critical violations (if enabled)

## API Reference

### Proctoring Log Endpoints

#### Store Proctoring Event
```http
POST /api/online-exams/{examUuid}/proctoring/logs
Content-Type: application/json

{
    "eventType": "webcam_capture",
    "severity": "info",
    "description": "Webcam image captured",
    "data": {
        "face_count": 1,
        "confidence": 0.95
    },
    "detectedAt": "2024-01-15T10:30:00Z",
    "ipAddress": "***********",
    "userAgent": "Mozilla/5.0..."
}
```

#### Get Proctoring Logs
```http
GET /api/online-exams/{examUuid}/proctoring/logs
Query Parameters:
- event_type: Filter by event type
- severity: Filter by severity level
- start_date: Start date filter
- end_date: End date filter
- per_page: Results per page
- export: Export as CSV (true/false)
```

#### Get Submission Summary
```http
GET /api/online-exams/{examUuid}/proctoring/submissions/{submissionUuid}/summary
```

### Configuration Endpoints

#### Validate Requirements
```http
POST /api/online-exams/{examUuid}/proctoring/validate-requirements
Content-Type: application/json

{
    "webcamAvailable": true,
    "microphoneAvailable": true,
    "screenRecordingAvailable": true,
    "browserCompatible": true
}
```

## Security & Privacy

### Data Protection

1. **Encryption**: All proctoring data is encrypted in transit and at rest
2. **Access Control**: Role-based access to proctoring data
3. **Audit Logging**: Complete audit trail of data access
4. **Data Minimization**: Only necessary data is collected

### Privacy Compliance

1. **Consent Management**: Explicit consent required before proctoring
2. **Data Retention**: Configurable retention periods with automatic cleanup
3. **Right to Access**: Students can request their proctoring data
4. **Right to Deletion**: Students can request data deletion (where legally permitted)

### Security Measures

1. **Secure Storage**: Media files stored with secure access controls
2. **IP Tracking**: IP address logging for security analysis
3. **Session Validation**: Continuous session integrity checks
4. **Violation Detection**: Real-time detection of suspicious activities

## Troubleshooting

### Common Issues

#### Webcam/Microphone Access Denied
**Problem**: Student cannot grant camera/microphone permissions
**Solution**: 
- Check browser permissions settings
- Ensure HTTPS connection
- Try different browser
- Check antivirus/firewall settings

#### Face Detection Not Working
**Problem**: Face detection consistently fails
**Solution**:
- Ensure adequate lighting
- Check camera positioning
- Verify browser compatibility
- Adjust detection sensitivity settings

#### High False Positive Rate
**Problem**: Too many false violation alerts
**Solution**:
- Adjust audio threshold settings
- Increase face detection failure tolerance
- Review custom instructions for clarity
- Consider environment-specific settings

#### Performance Issues
**Problem**: Exam interface is slow or unresponsive
**Solution**:
- Reduce capture frequency
- Disable unnecessary features
- Check network connectivity
- Clear browser cache

### System Maintenance

#### Regular Tasks
1. **Monitor Storage Usage**: Check media file storage consumption
2. **Review Logs**: Analyze system logs for errors
3. **Update Configurations**: Adjust settings based on usage patterns
4. **Clean Old Data**: Run cleanup commands for expired data

#### Cleanup Commands
```bash
# Clean up old proctoring logs (older than configured retention period)
php artisan proctoring:cleanup-logs

# Clean up old media files
php artisan proctoring:cleanup-media

# Generate proctoring usage report
php artisan proctoring:usage-report
```

### Support and Maintenance

For technical support:
1. Check system logs in `storage/logs/`
2. Review proctoring-specific logs
3. Verify browser compatibility
4. Test with different devices/networks
5. Contact system administrator for advanced troubleshooting

---

## Conclusion

The Online Exam Proctoring System provides a comprehensive solution for maintaining academic integrity in online examinations. With flexible configuration options, detailed monitoring capabilities, and robust security measures, it ensures fair and secure online testing while respecting student privacy and compliance requirements.

For additional support or feature requests, please contact your system administrator or refer to the technical documentation.
