<?php

namespace App\Models\Exam;

use App\Casts\DateTimeCast;
use App\Concerns\HasConfig;
use App\Concerns\HasFilter;
use App\Concerns\HasMeta;
use App\Concerns\HasUuid;
use App\Helpers\CalHelper;
use App\Models\Student\Student;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class OnlineExamSubmission extends Model
{
    use HasConfig, HasFactory, HasFilter, HasMeta, HasUuid, LogsActivity;

    protected $guarded = [];

    protected $primaryKey = 'id';

    protected $table = 'online_exam_submissions';

    protected $casts = [
        'started_at' => DateTimeCast::class,
        'submitted_at' => DateTimeCast::class,
        'evaluated_at' => DateTimeCast::class,
        'auto_submitted' => 'boolean',
        'answers' => 'array',
        'config' => 'array',
        'meta' => 'array',
    ];

    public function exam(): BelongsTo
    {
        return $this->belongsTo(OnlineExam::class, 'online_exam_id');
    }

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class, 'student_id');
    }

    public function proctorLogs(): HasMany
    {
        return $this->hasMany(OnlineExamProctorLog::class);
    }

    public function getRemainingTimeAttribute(): ?int
    {
        if (!$this->started_at->value || $this->submitted_at->value) {
            return null;
        }

        $exam = $this->exam;

        if (!$exam->is_flexible_timing) {
            // For traditional exams, use exam end time
            $endDate = $exam->end_date->value ?: $exam->date->value;
            $endDateTime = \Carbon\Carbon::parse($endDate.' '.$exam->end_time->value);
            return max(0, now()->diffInSeconds($endDateTime, false));
        }

        // For flexible exams, use allocated duration
        if (!$this->allocated_duration_minutes) {
            return null;
        }

        // Use CalHelper for proper timezone handling in multi-tenancy system
        $userTimezone = CalHelper::getTimezone();

        // Parse start time as stored (UTC) then convert to user timezone
        $startTime = \Carbon\Carbon::parse($this->started_at->value)->timezone($userTimezone);
        $endTime = $startTime->copy()->addMinutes($this->allocated_duration_minutes);

        // Get current time in user timezone
        $currentTime = \Carbon\Carbon::now($userTimezone);

        $remainingSeconds = max(0, $currentTime->diffInSeconds($endTime, false));

        return $remainingSeconds;
    }

    public function getIsExpiredAttribute(): bool
    {
        if (!$this->started_at->value || $this->submitted_at->value) {
            return false;
        }

        //return $this->remaining_time <= 0;
        $remainingTime = $this->remaining_time;
        $expired = $remainingTime <= 0;
        
        return $expired;
    }

    public function getTimeElapsedAttribute(): int
    {
        if (!$this->started_at->value) {
            return 0;
        }

        $endTime = $this->submitted_at->value ? \Carbon\Carbon::parse($this->submitted_at->value) : now();
        return \Carbon\Carbon::parse($this->started_at->value)->diffInSeconds($endTime);
    }

    public function getIndividualEndTimeAttribute(): ?\Carbon\Carbon
    {
        if (!$this->started_at->value || !$this->allocated_duration_minutes) {
            return null;
        }

        // Use CalHelper for proper timezone handling in multi-tenancy system
        $userTimezone = CalHelper::getTimezone();

        // Parse start time as stored (UTC) then convert to user timezone
        $startTime = \Carbon\Carbon::parse($this->started_at->value)->timezone($userTimezone);
        return $startTime->copy()->addMinutes($this->allocated_duration_minutes);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->useLogName('online_exam_submission')
            ->logAll()
            ->logExcept(['updated_at'])
            ->logOnlyDirty();
    }
}
