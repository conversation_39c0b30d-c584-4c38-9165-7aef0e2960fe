<?php

namespace App\Services\Exam;

use App\Enums\Exam\OnlineExamQuestionType;
use App\Enums\Exam\OnlineExamType;
use App\Models\Exam\OnlineExam;
use App\Models\Exam\OnlineExamSubmission;
use App\Models\Student\Student;
use App\Services\Exam\OnlineExamProctorService;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Validation\ValidationException;

class OnlineExamSubmitService
{
    private function isLive(OnlineExam $onlineExam): void
    {
        if ($onlineExam->is_flexible_timing) {
            if (!$onlineExam->is_available) {
                throw ValidationException::withMessages(['message' => trans('exam.online_exam.not_available')]);
            }
        } else {
            // For traditional exams, use the original simple check
            if (!$onlineExam->is_live) {
                throw ValidationException::withMessages(['message' => trans('user.errors.permission_denied')]);
            }
        }
    }

    public function getQuestions(OnlineExam $onlineExam): OnlineExam
    {
        $this->isLive($onlineExam);

        $student = $this->getStudent();

        // Check if student has already submitted
        $submission = OnlineExamSubmission::query()
            ->where('online_exam_id', $onlineExam->id)
            ->where('student_id', $student->id)
            ->first();

        $submittedAt = $submission?->submitted_at?->value ?? $submission?->submitted_at;
        if ($submission && $submittedAt) {
            throw ValidationException::withMessages(['message' => trans('exam.form.already_submitted')]);
        }

        // Check if individual time has expired (only for flexible exams)
        if ($onlineExam->is_flexible_timing && $submission && $submission->is_expired) {
            // Auto-submit expired exam with proper flow
            $this->autoSubmitExpiredSubmission($submission, $onlineExam);

            throw ValidationException::withMessages(['message' => trans('exam.online_exam.time_expired')]);
        }

        $onlineExam->load(['questions', 'media', 'submissions' => function ($query) use ($student) {
            $query->where('student_id', $student->id);
        }]);

        return $onlineExam;
    }

    private function getStudent(): Student
    {
        $student = Student::query()
            ->auth()
            ->first();

        if (! $student) {
            throw ValidationException::withMessages(['message' => trans('general.errors.invalid_action')]);
        }

        return $student;
    }

    public function startSubmission(Request $request, OnlineExam $onlineExam): mixed
    {
        $this->isLive($onlineExam);

        $student = $this->getStudent();

        $onlineExamRecord = OnlineExamSubmission::query()
            ->where('online_exam_id', $onlineExam->id)
            ->where('student_id', $student->id)
            ->first();

        if ($onlineExamRecord) {
            return $onlineExamRecord->started_at;
        }

        $startedAt = now()->toDateTimeString();

        $submissionData = [
            'online_exam_id' => $onlineExam->id,
            'student_id' => $student->id,
            'started_at' => $startedAt,
            'meta' => [
                'start_ip_address' => $request->ip(),
            ],
        ];

        // For flexible timing, store the allocated duration
        if ($onlineExam->is_flexible_timing && $onlineExam->duration_minutes) {
            $submissionData['allocated_duration_minutes'] = $onlineExam->duration_minutes;
        }

        $onlineExamRecord = OnlineExamSubmission::forceCreate($submissionData);

        // Initialize proctoring if enabled
        if ($onlineExam->enable_proctoring) {
            $proctorService = app(OnlineExamProctorService::class);
            $proctorService->initializeProctoring($onlineExamRecord);
        }

        return \Cal::dateTime($startedAt);
    }

    public function store(Request $request, OnlineExam $onlineExam): mixed
    {
        // Skip time validation for auto-submit
        $isAutoSubmit = $request->boolean('auto_submit', false);
        if (!$isAutoSubmit) {
            $this->isLive($onlineExam);
        }

        $student = $this->getStudent();

        $onlineExamRecord = OnlineExamSubmission::query()
            ->where('online_exam_id', $onlineExam->id)
            ->where('student_id', $student->id)
            ->first();

        if (! $onlineExamRecord) {
            throw ValidationException::withMessages(['message' => trans('general.errors.invalid_action')]);
        }

        // Check if already submitted
        $submittedAt = $onlineExamRecord->submitted_at?->value ?? $onlineExamRecord->submitted_at;
        if ($submittedAt) {
            throw ValidationException::withMessages(['message' => trans('exam.form.already_submitted')]);
        }

        // Check if submission time has expired (only for flexible exams)
        if (!$isAutoSubmit &&  $onlineExam->is_flexible_timing && $onlineExamRecord->is_expired) {
            // Auto-submit expired exam with proper flow
            $this->autoSubmitExpiredSubmission($onlineExamRecord, $onlineExam);

            throw ValidationException::withMessages(['message' => trans('exam.online_exam.time_expired')]);
        }

        $questions = $onlineExam->questions;

        $inputQuestions = collect($request->questions ?? []);

        $inputQuestions = $inputQuestions->filter(function ($question) use ($questions) {
            return in_array($question['uuid'], $questions->pluck('uuid')->all());
        });

        $totalObtainedMark = 0;
        $submittedAnswerCount = 0;

        $answers = $inputQuestions
            ->map(function ($inputQuestion) use ($questions, $onlineExam) {
                $question = $questions->firstWhere('uuid', $inputQuestion['uuid']);

                $isCorrect = null;
                $obtainedMark = 0;
                if ($question->type == OnlineExamQuestionType::MCQ) {
                    $correctOption = Arr::get(collect($question->options)->firstWhere('is_correct', true), 'title');
                    $isCorrect = $correctOption == $inputQuestion['answer'];

                    if ($isCorrect) {
                        $obtainedMark = $question->mark;
                    } elseif (! $isCorrect && $onlineExam->getConfig('has_negative_marking', false)) {
                        $obtainedMark = -1 * round($question->mark * ($onlineExam->getConfig('negative_mark_percent_per_question', 0) / 100), 2);
                    }
                }

                return [
                    'uuid' => $inputQuestion['uuid'],
                    'answer' => $inputQuestion['answer'],
                    'is_correct' => $isCorrect,
                    'obtained_mark' => $obtainedMark,
                ];
            });

        $totalObtainedMark = $answers->sum('obtained_mark');

        if ($onlineExam->type == OnlineExamType::MCQ) {
            $onlineExamRecord->evaluated_at = now()->toDateTimeString();
        }
        $onlineExamRecord->obtained_mark = $totalObtainedMark;
        $onlineExamRecord->answers = $answers;
        $onlineExamRecord->save();

        $submittedAnswerCount = collect($answers)->filter(function ($answer) {
            return ! empty($answer['answer']);
        })->count();

        return $submittedAnswerCount;
    }

    public function finishSubmission(Request $request, OnlineExam $onlineExam): mixed
    {
        $student = $this->getStudent();

        $onlineExamRecord = OnlineExamSubmission::query()
            ->where('online_exam_id', $onlineExam->id)
            ->where('student_id', $student->id)
            ->first();

        if (! $onlineExamRecord) {
            throw ValidationException::withMessages(['message' => trans('general.errors.invalid_action')]);
        }

        // Check if already submitted
        $submittedAt = $onlineExamRecord->submitted_at?->value ?? $onlineExamRecord->submitted_at;
        if ($submittedAt) {
            throw ValidationException::withMessages(['message' => trans('exam.online_exam.already_submitted')]);
        }

        // Allow finishing even if time expired (for manual submission)
        // but mark it appropriately

        $onlineExamRecord->submitted_at = now()->toDateTimeString();

        // Check if this is an auto-submit
        $isAutoSubmit = $request->boolean('auto_submit', false);
        if ($isAutoSubmit) {
            $onlineExamRecord->auto_submitted = true;
        }

        if ($onlineExam->type == OnlineExamType::MCQ) {
            $onlineExamRecord->evaluated_at = now()->toDateTimeString();
        }

        $metadata = [
            'end_ip_address' => $request->ip(),
        ];

        if ($isAutoSubmit) {
            $metadata['auto_submitted_reason'] = 'Time expired - auto submit';
            $metadata['auto_submitted_at'] = now()->toDateTimeString();
        }

        $onlineExamRecord->setMeta($metadata);
        $onlineExamRecord->save();

        // Finalize proctoring if enabled
        if ($onlineExam->enable_proctoring) {
            $proctorService = app(OnlineExamProctorService::class);
            $proctorService->finalizeProctoring($onlineExamRecord);
        }

        return $onlineExamRecord->submitted_at;
    }

    public function getTimeStatus(OnlineExam $onlineExam): array
    {
        $student = $this->getStudent();

        $submission = OnlineExamSubmission::query()
            ->where('online_exam_id', $onlineExam->id)
            ->where('student_id', $student->id)
            ->first();

        if (!$submission || !$submission->started_at->value) {
            return [
                'started' => false,
                'remaining_time' => null,
                'expired' => false,
            ];
        }

        $remainingTime = $submission->remaining_time;
        $isExpired = $submission->is_expired;

        // For traditional exams, also check if exam itself has ended
        if (!$onlineExam->is_flexible_timing && !$isExpired) {
            $isExpired = !$onlineExam->is_live && $onlineExam->is_completed;
        }

        return [
            'started' => true,
            'remaining_time' => $remainingTime,
            'expired' => $isExpired,
            'individual_end_time' => $submission->individual_end_time?->toDateTimeString(),
            'auto_submitted' => $submission->auto_submitted,
        ];
    }

    /**
     * Auto-submit expired submission with proper flow
     */
    private function autoSubmitExpiredSubmission(OnlineExamSubmission $submission, OnlineExam $onlineExam): void
    {
        // Preserve any existing answers (don't overwrite)
        if (empty($submission->answers)) {
            $submission->answers = [];
        }

        // Mark as submitted
        $submission->submitted_at = now()->toDateTimeString();
        $submission->auto_submitted = true;

        // Auto-evaluate MCQ exams
        if ($onlineExam->type->value === 'mcq') {
            $submission->evaluated_at = now()->toDateTimeString();
        }

        $submission->setMeta([
            'auto_submitted_reason' => 'Time expired during access',
            'auto_submitted_at' => now()->toDateTimeString(),
        ]);

        $submission->save();
    }
}
