<?php

namespace App\Http\Requests\Exam;

use App\Enums\Exam\OnlineExamType;
use App\Helpers\CalHelper;
use App\Models\Academic\Batch;
use App\Models\Academic\Subject;
use App\Models\Exam\OnlineExam;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class OnlineExamRequest extends FormRequest
{
    /**
     * Deexamine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'title' => ['required', 'min:2', 'max:255'],
            'type' => ['required', new Enum(OnlineExamType::class)],
            'is_flexible_timing' => ['boolean'],
            'batches' => 'array|min:1',
            'subject' => 'nullable|uuid',
            'pass_percentage' => ['required', 'numeric', 'min:0', 'max:100'],
            'has_negative_marking' => ['boolean'],
            'negative_mark_percent_per_question' => ['required_if:has_negative_marking,true', 'numeric', 'min:0', 'max:100'],
            'instructions' => ['required', 'min:2', 'max:10000'],
            'description' => ['nullable', 'min:2', 'max:10000'],
            'enable_proctoring' => ['boolean'],
            'proctor_config' => ['nullable', 'array'],
            'proctor_config.webcam_monitoring' => ['boolean'],
            'proctor_config.microphone_monitoring' => ['boolean'],
            'proctor_config.screen_recording' => ['boolean'],
            'proctor_config.fullscreen_enforcement' => ['boolean'],
            'proctor_config.copy_paste_blocking' => ['boolean'],
            'proctor_config.face_detection' => ['boolean'],
            'proctor_config.capture_interval_seconds' => ['integer', 'min:10', 'max:300'],
            'proctor_config.audio_threshold_db' => ['integer', 'min:-80', 'max:0'],
            'proctor_config.max_face_detection_failures' => ['integer', 'min:1', 'max:20'],
            'proctor_config.allow_tab_switching' => ['boolean'],
            'proctor_config.auto_submit_on_violations' => ['boolean'],
            'proctor_config.custom_instructions' => ['nullable', 'string', 'max:1000'],
        ];

        if ($this->boolean('is_flexible_timing', false)) {
            // Flexible timing validation
            $rules['duration_minutes'] = ['required', 'integer', 'min:1', 'max:1000']; // Max 8 hours
            $rules['expiry_date'] = ['nullable', 'date_format:Y-m-d', 'after_or_equal:today'];
            $rules['expiry_time'] = ['nullable', 'date_format:H:i:s'];
            $rules['auto_publish_results_for_flexible_timing'] = ['boolean'];
        } else {
            // Traditional timing validation
            $rules['date'] = ['required', 'date_format:Y-m-d'];
            $rules['start_time'] = ['required', 'date_format:H:i:s'];
            $rules['end_date'] = ['nullable', 'date_format:Y-m-d', 'after_or_equal:date'];
            $rules['end_time'] = ['required', 'date_format:H:i:s'];

            if (!$this->end_date) {
                $rules['end_time'] = ['required', 'date_format:H:i:s', 'after:start_time'];
            }
        }

        return $rules;
    }

    public function withValidator($validator)
    {
        if (! $validator->passes()) {
            return;
        }

        $validator->after(function ($validator) {
            $uuid = $this->route('online_exam');

            // Only validate start time for traditional timing
            if (!$this->boolean('is_flexible_timing', false)) {
                $dateTime = Carbon::parse(CalHelper::storeDateTime($this->date.' '.$this->start_time));

                if ($dateTime->isPast()) {
                    $validator->errors()->add('date', trans('validation.after', ['attribute' => __('exam.online_exam.props.date'), 'date' => \Cal::dateTime(now()->toDateTimeString())->formatted]));
                }
            } else {
                // Validate flexible timing specific rules
                if ($this->expiry_date && $this->expiry_time) {
                    $expiryDateTime = Carbon::parse($this->expiry_date.' '.$this->expiry_time);
                    if ($expiryDateTime->isPast()) {
                        $validator->errors()->add('expiry_date', trans('validation.after', ['attribute' => __('exam.online_exam.props.expiry_date'), 'date' => \Cal::dateTime(now()->toDateTimeString())->formatted]));
                    }
                }
            }

            $batches = Batch::query()
                ->byPeriod()
                ->filterAccessible()
                ->whereIn('uuid', $this->batches)
                ->listOrFail(trans('academic.batch.batch'), 'batches');

            $subject = null;
            if ($this->subject) {
                foreach ($batches as $batch) {
                    $subject = Subject::query()
                        ->findByBatchOrFail($batch->id, $batch->course_id, $this->subject);
                }
            }

            $existingRecords = OnlineExam::query()
                ->byPeriod()
                ->when($uuid, function ($q, $uuid) {
                    $q->where('uuid', '!=', $uuid);
                })
                ->whereTitle($this->title)
                ->whereDate('date', $this->date)
                ->exists();

            if ($existingRecords) {
                $validator->errors()->add('title', trans('validation.unique', ['attribute' => __('exam.online_exam.props.title')]));
            }

            $this->merge([
                'batch_ids' => $batches->pluck('id')->all(),
                'subject_id' => $subject?->id,
            ]);
        });
    }

    /**
     * Translate fields with user friendly name.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'title' => __('exam.online_exam.props.title'),
            'type' => __('exam.online_exam.props.type'),
            'date' => __('exam.online_exam.props.date'),
            'start_time' => __('exam.online_exam.props.start_time'),
            'end_date' => __('exam.online_exam.props.end_date'),
            'end_time' => __('exam.online_exam.props.end_time'),
            'batches' => __('academic.batch.batch'),
            'subject' => __('academic.subject.subject'),
            'pass_percentage' => __('exam.online_exam.props.pass_percentage'),
            'has_negative_marking' => __('exam.online_exam.props.has_negative_marking'),
            'negative_mark_percent_per_question' => __('exam.online_exam.props.negative_mark_percent_per_question'),
            'auto_publish_results_for_flexible_timing' => __('exam.online_exam.props.auto_publish_results_for_flexible_timing'),
            'instructions' => __('exam.online_exam.props.instructions'),
            'description' => __('exam.online_exam.props.description'),
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'negative_mark_percent_per_question.required_if' => __('validation.required', ['attribute' => __('exam.online_exam.props.negative_mark_percent_per_question')]),
        ];
    }
}
