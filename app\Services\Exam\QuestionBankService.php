<?php

namespace App\Services\Exam;

use App\Enums\Exam\OnlineExamQuestionType;
use App\Http\Resources\Academic\BatchResource;
use App\Http\Resources\Academic\SubjectResource;
use App\Models\Academic\Batch;
use App\Models\Academic\Subject;
use App\Models\Exam\OnlineExam;
use App\Models\Exam\OnlineExamQuestion;
use App\Models\Exam\QuestionBank;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class QuestionBankService
{
    public function preRequisite(Request $request): array
    {
        $subjects = SubjectResource::collection(Subject::query()->byPeriod()->get());

        $types = OnlineExamQuestionType::getOptions();

        return compact('subjects', 'types');
    }

    public function preRequisiteForFilter(Request $request): array
    {
        $subjects = SubjectResource::collection(Subject::query()
            ->byPeriod()
            ->get());

        $batches = BatchResource::collection(Batch::query()
            ->byPeriod()
            ->filterAccessible()
            ->get());

        $types = OnlineExamQuestionType::getOptions();

        return compact('subjects', 'batches', 'types');
    }

    public function create(Request $request): QuestionBank
    {
        \DB::beginTransaction();

        $questionBank = QuestionBank::forceCreate($this->formatParams($request));

        $this->updateBatchRelations($request, $questionBank);

        \DB::commit();

        return $questionBank;
    }

    public function findByUuidOrFail(string $uuid): QuestionBank
    {
        return QuestionBank::query()
            ->with(['subject', 'batches.course'])
            ->where('uuid', $uuid)
            ->filterAccessible()
            ->firstOrFail();
    }

    public function update(Request $request, QuestionBank $questionBank): void
    {
        \DB::beginTransaction();

        $questionBank->forceFill($this->formatParams($request, $questionBank))->save();

        $this->updateBatchRelations($request, $questionBank);

        \DB::commit();
    }

    public function deletable(QuestionBank $questionBank): bool
    {
        // Check if question bank is used in any online exams
        $usedInExams = OnlineExamQuestion::query()
            ->where('meta->question_bank_id', $questionBank->id)
            ->exists();

        return !$usedInExams;
    }

    public function delete(QuestionBank $questionBank): void
    {
        if (!$this->deletable($questionBank)) {
            throw ValidationException::withMessages(['message' => trans('exam.question_bank.errors.cannot_delete_used_question')]);
        }

        \DB::beginTransaction();

        $questionBank->batches()->detach();
        $questionBank->delete();

        \DB::commit();
    }

    public function importToExam(Request $request, OnlineExam $onlineExam): array
    {
        $questionIds = $request->input('question_ids', []);
        $marks = $request->input('marks', []);

        if (empty($questionIds)) {
            throw ValidationException::withMessages(['message' => trans('exam.question_bank.errors.no_questions_selected')]);
        }

        \DB::beginTransaction();

        $query = QuestionBank::query()
            ->whereIn('uuid', $questionIds)
            ->filterAccessible();

        // Filter by question type if the exam is of type MCQ
        if ($onlineExam->type === OnlineExamQuestionType::MCQ) {
            $query->where('type', OnlineExamQuestionType::MCQ);
        }

        $questions = $query->get();

        $importedCount = 0;
        $maxPosition = OnlineExamQuestion::where('online_exam_id', $onlineExam->id)->max('position') ?? 0;

        foreach ($questions as $question) {
            $mark = $marks[$question->uuid] ?? $question->mark;
            // Transform options structure for OnlineExamQuestion
            $transformedOptions = $this->transformOptionsForOnlineExam($question->type, $question->options);

            OnlineExamQuestion::create([
                'online_exam_id' => $onlineExam->id,
                'type' => $question->type,
                'title' => $question->title,
                'header' => $question->header,
                'description' => $question->description,
                'mark' => $mark,
                'options' => $transformedOptions,
                'position' => ++$maxPosition,
                'meta' => [
                    'question_bank_id' => $question->id,
                    'imported_at' => now()->toISOString(),
                ],
            ]);

            $importedCount++;
        }

        // Update exam max_mark
        $this->updateExamMaxMark($onlineExam);

        \DB::commit();

        return [
            'imported_count' => $importedCount,
            'total_questions' => $questions->count(),
        ];
    }

    private function formatParams(Request $request, ?QuestionBank $questionBank = null): array
    {
        $formatted = [
            'subject_id' => Subject::query()->byPeriod()->where('uuid', $request->subject)->value('id'),
            'type' => $request->type,
            'title' => $request->title,
            'header' => $request->header,
            'description' => $request->description,
            'mark' => $request->mark,
            'options' => $request->options,
        ];

        return $formatted;
    }

    private function updateBatchRelations(Request $request, QuestionBank $questionBank): void
    {
        $batchIds = [];

        if ($request->has('batches') && is_array($request->batches)) {
            $batchIds = Batch::query()
                ->byPeriod()
                ->filterAccessible()
                ->whereIn('uuid', $request->batches)
                ->pluck('id')
                ->toArray();
        }

        // Prepare sync data with UUIDs for pivot table
        $syncData = [];
        foreach ($batchIds as $batchId) {
            $syncData[$batchId] = [
                'uuid' => \Str::uuid(),
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        $questionBank->batches()->sync($syncData);
    }

    private function updateExamMaxMark(OnlineExam $onlineExam): void
    {
        $totalMarks = OnlineExamQuestion::where('online_exam_id', $onlineExam->id)
            ->sum('mark');

        $onlineExam->update(['max_mark' => $totalMarks]);
    }

    /**
     * Transform options from QuestionBank format to OnlineExamQuestion format
     */
    private function transformOptionsForOnlineExam($questionType, $options): array
    {

        // For non-MCQ questions, return empty array
        if ($questionType !== OnlineExamQuestionType::MCQ) {
            return [];
        }

        // For MCQ questions, add UUID to each option
        if (!is_array($options)) {
            return [];
        }

        $transformed = array_map(function ($option) {
            $result = [
                'uuid' => \Str::uuid(),
                'title' => $option['title'] ?? '',
                'is_correct' => filter_var($option['is_correct'], FILTER_VALIDATE_BOOLEAN),
            ];
            return $result;
        }, $options);

        return $transformed;
    }
}
