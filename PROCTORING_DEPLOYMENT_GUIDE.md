# Proctoring System Deployment Guide

## Overview

This guide covers the deployment, configuration, and maintenance of the Online Exam Proctoring System in production environments.

## Pre-Deployment Checklist

### System Requirements
- [ ] PHP 8.1+ with required extensions
- [ ] MySQL 8.0+ or PostgreSQL 13+
- [ ] Redis for caching and queues
- [ ] Node.js 18+ for frontend assets
- [ ] SSL certificate for HTTPS
- [ ] Sufficient storage for media files (recommend 100GB+)

### Security Requirements
- [ ] HTTPS enabled and enforced
- [ ] Firewall configured
- [ ] Database access restricted
- [ ] File upload security configured
- [ ] Rate limiting enabled
- [ ] CORS properly configured

### Performance Requirements
- [ ] Adequate server resources (4+ CPU cores, 8GB+ RAM)
- [ ] CDN configured for media delivery
- [ ] Database optimization completed
- [ ] Caching strategy implemented

## Deployment Steps

### 1. Environment Configuration

#### Environment Variables
```env
# Proctoring Configuration
PROCTORING_ENABLED=true
PROCTORING_MEDIA_DISK=public
PROCTORING_MEDIA_RETENTION_DAYS=90
PROCTORING_LOG_RETENTION_DAYS=365
PROCTORING_AUTO_CLEANUP=true
PROCTORING_MAX_FILE_SIZE=10240
PROCTORING_ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,mp3,wav,ogg,mp4,webm

# Storage Configuration
FILESYSTEM_DISK=s3
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=your-proctoring-bucket
AWS_USE_PATH_STYLE_ENDPOINT=false

# Queue Configuration
QUEUE_CONNECTION=redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Rate Limiting
PROCTORING_RATE_LIMIT_LOGS=60
PROCTORING_RATE_LIMIT_UPLOADS=10
PROCTORING_RATE_LIMIT_RETRIEVAL=100

# Security
PROCTORING_REQUIRE_HTTPS=true
PROCTORING_ENABLE_VIRUS_SCAN=true
PROCTORING_MAX_VIOLATIONS_PER_HOUR=50
```

### 2. Database Migration

#### Run Migrations
```bash
# Backup existing database
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# Run migrations
php artisan migrate

# Verify migration status
php artisan migrate:status
```

#### Seed Permissions
```bash
php artisan db:seed --class=ProctorPermissionSeeder
```

### 3. Storage Configuration

#### Local Storage Setup
```bash
# Create storage directories
mkdir -p storage/app/public/proctoring/{images,audio,video}
chmod -R 755 storage/app/public/proctoring

# Create symbolic link
php artisan storage:link
```

#### S3 Storage Setup
```bash
# Install AWS SDK
composer require league/flysystem-aws-s3-v3

# Configure S3 bucket policy
aws s3api put-bucket-policy --bucket your-proctoring-bucket --policy file://s3-policy.json
```

**S3 Bucket Policy (s3-policy.json):**
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "ProctorMediaAccess",
            "Effect": "Allow",
            "Principal": {
                "AWS": "arn:aws:iam::ACCOUNT-ID:user/proctoring-user"
            },
            "Action": [
                "s3:GetObject",
                "s3:PutObject",
                "s3:DeleteObject"
            ],
            "Resource": "arn:aws:s3:::your-proctoring-bucket/proctoring/*"
        }
    ]
}
```

### 4. Queue Configuration

#### Redis Setup
```bash
# Install Redis
sudo apt-get install redis-server

# Configure Redis
sudo nano /etc/redis/redis.conf
# Set: maxmemory 1gb
# Set: maxmemory-policy allkeys-lru

# Start Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

#### Queue Workers
```bash
# Create supervisor configuration
sudo nano /etc/supervisor/conf.d/proctoring-worker.conf
```

**Supervisor Configuration:**
```ini
[program:proctoring-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /path/to/your/app/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=4
redirect_stderr=true
stdout_logfile=/path/to/your/app/storage/logs/worker.log
stopwaitsecs=3600
```

```bash
# Update supervisor
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start proctoring-worker:*
```

### 5. Frontend Assets

#### Build Production Assets
```bash
# Install dependencies
npm ci --production

# Build assets
npm run build

# Verify assets
ls -la public/build/
```

#### CDN Configuration (Optional)
```bash
# Upload assets to CDN
aws s3 sync public/build/ s3://your-cdn-bucket/build/ --delete

# Update asset URLs in .env
ASSET_URL=https://cdn.yourdomain.com
```

### 6. Web Server Configuration

#### Nginx Configuration
```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    root /path/to/your/app/public;
    index index.php;

    # SSL Configuration
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # File Upload Configuration
    client_max_body_size 20M;
    client_body_timeout 60s;

    # Proctoring Media Location
    location /storage/proctoring/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options "nosniff";
        
        # Security: Only allow authenticated access
        auth_request /auth-check;
    }

    # Auth check endpoint
    location = /auth-check {
        internal;
        proxy_pass http://127.0.0.1:8000/api/auth/check;
        proxy_pass_request_body off;
        proxy_set_header Content-Length "";
        proxy_set_header X-Original-URI $request_uri;
    }

    # PHP-FPM Configuration
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_read_timeout 300;
    }

    # Rate Limiting
    location /api/online-exams {
        limit_req zone=api burst=20 nodelay;
        try_files $uri $uri/ /index.php?$query_string;
    }
}

# Rate limiting zone
http {
    limit_req_zone $binary_remote_addr zone=api:10m rate=60r/m;
}
```

### 7. Monitoring and Logging

#### Log Configuration
```bash
# Create log rotation
sudo nano /etc/logrotate.d/proctoring
```

**Log Rotation Configuration:**
```
/path/to/your/app/storage/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        /usr/bin/supervisorctl restart proctoring-worker:*
    endscript
}
```

#### Monitoring Setup
```bash
# Install monitoring tools
sudo apt-get install htop iotop nethogs

# Setup log monitoring
tail -f storage/logs/laravel.log | grep -i proctoring
```

### 8. Security Hardening

#### File Permissions
```bash
# Set correct permissions
sudo chown -R www-data:www-data /path/to/your/app
sudo chmod -R 755 /path/to/your/app
sudo chmod -R 775 /path/to/your/app/storage
sudo chmod -R 775 /path/to/your/app/bootstrap/cache
```

#### Firewall Configuration
```bash
# Configure UFW
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw deny 3306/tcp  # Deny direct database access
sudo ufw enable
```

#### Database Security
```sql
-- Create dedicated proctoring user
CREATE USER 'proctoring_user'@'localhost' IDENTIFIED BY 'strong_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON database_name.* TO 'proctoring_user'@'localhost';
FLUSH PRIVILEGES;
```

## Post-Deployment Configuration

### 1. System Verification

#### Health Check Script
```bash
#!/bin/bash
# proctoring-health-check.sh

echo "Checking Proctoring System Health..."

# Check database connection
php artisan tinker --execute="DB::connection()->getPdo();" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✓ Database connection: OK"
else
    echo "✗ Database connection: FAILED"
fi

# Check storage
if [ -d "storage/app/public/proctoring" ]; then
    echo "✓ Storage directory: OK"
else
    echo "✗ Storage directory: MISSING"
fi

# Check queue workers
if pgrep -f "queue:work" > /dev/null; then
    echo "✓ Queue workers: RUNNING"
else
    echo "✗ Queue workers: NOT RUNNING"
fi

# Check Redis
redis-cli ping > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✓ Redis: OK"
else
    echo "✗ Redis: FAILED"
fi

echo "Health check complete."
```

#### API Testing
```bash
# Test proctoring endpoints
curl -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"eventType":"webcam_capture","severity":"info","description":"Test","detectedAt":"2024-01-15T10:30:00Z"}' \
     https://yourdomain.com/api/online-exams/test-uuid/proctoring/logs
```

### 2. Performance Optimization

#### Database Optimization
```sql
-- Add indexes for proctoring queries
CREATE INDEX idx_proctor_logs_exam_id ON proctor_logs(online_exam_id);
CREATE INDEX idx_proctor_logs_student_id ON proctor_logs(student_id);
CREATE INDEX idx_proctor_logs_detected_at ON proctor_logs(detected_at);
CREATE INDEX idx_proctor_logs_severity ON proctor_logs(severity);
CREATE INDEX idx_proctor_logs_event_type ON proctor_logs(event_type);

-- Optimize table
OPTIMIZE TABLE proctor_logs;
```

#### Cache Configuration
```bash
# Configure Redis for caching
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### 3. Backup Strategy

#### Database Backup
```bash
#!/bin/bash
# backup-proctoring-db.sh

BACKUP_DIR="/backups/proctoring"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="your_database"

mkdir -p $BACKUP_DIR

# Full backup
mysqldump -u username -p$password $DB_NAME > $BACKUP_DIR/full_backup_$DATE.sql

# Proctoring tables only
mysqldump -u username -p$password $DB_NAME proctor_logs online_exams > $BACKUP_DIR/proctoring_backup_$DATE.sql

# Compress backups
gzip $BACKUP_DIR/*_$DATE.sql

# Remove old backups (keep 30 days)
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete
```

#### Media Backup
```bash
#!/bin/bash
# backup-proctoring-media.sh

if [ "$FILESYSTEM_DISK" = "s3" ]; then
    # S3 backup
    aws s3 sync s3://your-proctoring-bucket s3://your-backup-bucket --delete
else
    # Local backup
    rsync -av storage/app/public/proctoring/ /backups/proctoring-media/
fi
```

## Maintenance Tasks

### Daily Tasks
```bash
# Check system health
./proctoring-health-check.sh

# Monitor disk usage
df -h | grep -E "(storage|proctoring)"

# Check queue status
php artisan queue:monitor
```

### Weekly Tasks
```bash
# Database maintenance
php artisan proctoring:cleanup-logs
php artisan proctoring:cleanup-media

# Performance analysis
php artisan proctoring:usage-report

# Security audit
php artisan proctoring:security-check
```

### Monthly Tasks
```bash
# Full system backup
./backup-proctoring-db.sh
./backup-proctoring-media.sh

# Performance optimization
php artisan proctoring:optimize-database

# Security updates
composer update --with-dependencies
npm audit fix
```

## Troubleshooting

### Common Issues

#### High Storage Usage
```bash
# Check storage usage
du -sh storage/app/public/proctoring/*

# Clean old files
php artisan proctoring:cleanup-media --days=30
```

#### Queue Backlog
```bash
# Check queue status
php artisan queue:monitor

# Clear failed jobs
php artisan queue:flush

# Restart workers
sudo supervisorctl restart proctoring-worker:*
```

#### Database Performance
```sql
-- Check slow queries
SHOW PROCESSLIST;

-- Analyze table performance
ANALYZE TABLE proctor_logs;

-- Check index usage
EXPLAIN SELECT * FROM proctor_logs WHERE online_exam_id = 'uuid';
```

## Scaling Considerations

### Horizontal Scaling
- Load balancer configuration
- Database read replicas
- Distributed file storage
- Queue worker scaling

### Vertical Scaling
- Server resource monitoring
- Database optimization
- Cache layer enhancement
- CDN implementation

## Security Monitoring

### Log Analysis
```bash
# Monitor for suspicious activity
grep -i "proctoring" /var/log/nginx/access.log | grep -E "(40[0-9]|50[0-9])"

# Check for failed authentication
grep "Unauthenticated" storage/logs/laravel.log
```

### Automated Alerts
```bash
# Setup monitoring alerts
# - High error rates
# - Storage usage > 80%
# - Queue backlog > 1000 jobs
# - Failed authentication attempts > 100/hour
```
