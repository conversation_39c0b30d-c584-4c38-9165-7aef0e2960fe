<template>
    <Transition
        enter-active-class="transition ease-out duration-300"
        enter-from-class="opacity-0 transform scale-95"
        enter-to-class="opacity-100 transform scale-100"
        leave-active-class="transition ease-in duration-200"
        leave-from-class="opacity-100 transform scale-100"
        leave-to-class="opacity-0 transform scale-95"
    >
        <div
            v-if="visible"
            class="fixed top-20 right-4 z-50 max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg border border-gray-200 dark:border-gray-700"
        >
            <div class="p-4">
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <svg
                            v-if="type === 'warning'"
                            class="w-6 h-6 text-yellow-400"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                        >
                            <path
                                fill-rule="evenodd"
                                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                clip-rule="evenodd"
                            ></path>
                        </svg>
                        <svg
                            v-else-if="type === 'error'"
                            class="w-6 h-6 text-red-400"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                        >
                            <path
                                fill-rule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                clip-rule="evenodd"
                            ></path>
                        </svg>
                        <svg
                            v-else
                            class="w-6 h-6 text-blue-400"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                        >
                            <path
                                fill-rule="evenodd"
                                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                                clip-rule="evenodd"
                            ></path>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h3
                            class="text-sm font-medium"
                            :class="{
                                'text-yellow-800 dark:text-yellow-200': type === 'warning',
                                'text-red-800 dark:text-red-200': type === 'error',
                                'text-blue-800 dark:text-blue-200': type === 'info'
                            }"
                        >
                            {{ title }}
                        </h3>
                        <p
                            class="mt-1 text-sm"
                            :class="{
                                'text-yellow-700 dark:text-yellow-300': type === 'warning',
                                'text-red-700 dark:text-red-300': type === 'error',
                                'text-blue-700 dark:text-blue-300': type === 'info'
                            }"
                        >
                            {{ message }}
                        </p>
                        <div v-if="actions.length > 0" class="mt-3 flex space-x-2">
                            <BaseButton
                                v-for="action in actions"
                                :key="action.label"
                                :design="action.design || 'primary'"
                                size="xs"
                                @click="handleAction(action)"
                            >
                                {{ action.label }}
                            </BaseButton>
                        </div>
                    </div>
                    <div class="flex-shrink-0">
                        <button
                            @click="close"
                            class="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                            <span class="sr-only">Close</span>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    fill-rule="evenodd"
                                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                    clip-rule="evenodd"
                                ></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </Transition>
</template>

<script>
export default {
    name: "ProctorAlert",
}
</script>

<script setup>
import { ref, onMounted } from 'vue'

const props = defineProps({
    type: {
        type: String,
        default: 'info',
        validator: (value) => ['info', 'warning', 'error'].includes(value)
    },
    title: {
        type: String,
        required: true
    },
    message: {
        type: String,
        required: true
    },
    actions: {
        type: Array,
        default: () => []
    },
    autoClose: {
        type: Number,
        default: 5000 // 5 seconds
    },
    persistent: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['close', 'action'])

const visible = ref(false)

const close = () => {
    visible.value = false
    setTimeout(() => {
        emit('close')
    }, 200)
}

const handleAction = (action) => {
    emit('action', action)
    if (action.closeOnClick !== false) {
        close()
    }
}

onMounted(() => {
    visible.value = true
    
    if (!props.persistent && props.autoClose > 0) {
        setTimeout(() => {
            close()
        }, props.autoClose)
    }
})
</script>
