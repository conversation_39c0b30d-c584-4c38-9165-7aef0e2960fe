# Proctoring System Testing Guide

## Overview

This guide provides comprehensive testing procedures for the Online Exam Proctoring System, including unit tests, feature tests, integration tests, and manual testing scenarios.

## Test Structure

### Backend Tests
```
tests/
├── Unit/
│   ├── Models/
│   │   ├── ProctorLogTest.php
│   │   └── OnlineExamProctorTest.php
│   └── Services/
│       └── ProctorLogServiceTest.php
├── Feature/
│   └── Exam/
│       ├── ProctorLogTest.php
│       └── OnlineExamProctorConfigTest.php
└── Integration/
    └── ProctorSystemIntegrationTest.php
```

### Frontend Tests
```
tests/
├── unit/
│   └── components/
│       └── Exam/
│           ├── ProctorConfigurationPanel.spec.js
│           ├── ProctorRequirementsCheck.spec.js
│           └── ProctorReviewDashboard.spec.js
└── e2e/
    └── proctoring/
        ├── exam-creation.spec.js
        ├── proctoring-session.spec.js
        └── review-dashboard.spec.js
```

## Running Tests

### Backend Tests

#### Run All Tests
```bash
php artisan test
```

#### Run Specific Test Suites
```bash
# Unit tests only
php artisan test --testsuite=Unit

# Feature tests only
php artisan test --testsuite=Feature

# Proctoring-specific tests
php artisan test tests/Unit/Models/ProctorLogTest.php
php artisan test tests/Feature/Exam/ProctorLogTest.php
```

#### Run Tests with Coverage
```bash
php artisan test --coverage
php artisan test --coverage-html coverage-report
```

### Frontend Tests

#### Unit Tests
```bash
npm run test:unit
npm run test:unit -- --watch
npm run test:unit -- --coverage
```

#### End-to-End Tests
```bash
npm run test:e2e
npm run test:e2e:headless
```

## Test Scenarios

### 1. Model Tests

#### ProctorLog Model Tests
- ✅ Can create a proctor log with all required fields
- ✅ Validates relationships (exam, student, submission)
- ✅ Filters by event type and severity
- ✅ Scopes work correctly (forExam, forStudent)
- ✅ Media handling (hasMedia, getMediaUrl)
- ✅ Data casting (JSON fields)

#### OnlineExam Model Tests
- ✅ Proctoring configuration storage and retrieval
- ✅ Helper methods (hasWebcamMonitoring, hasFaceDetection, etc.)
- ✅ Default configuration handling
- ✅ Configuration validation

### 2. Service Tests

#### ProctorLogService Tests
- ✅ Create logs with and without media
- ✅ Filter and search functionality
- ✅ Summary generation
- ✅ Timeline creation
- ✅ CSV export functionality
- ✅ Media file handling and storage
- ✅ Cleanup operations

### 3. API Tests

#### Proctoring Log API Tests
- ✅ Store proctoring events (with validation)
- ✅ Upload media files with events
- ✅ Retrieve logs with filtering and pagination
- ✅ Export logs as CSV
- ✅ Authentication and authorization
- ✅ Error handling and validation

#### Configuration API Tests
- ✅ Create exams with proctoring configuration
- ✅ Update proctoring settings
- ✅ Validate configuration fields
- ✅ Retrieve configuration data

### 4. Integration Tests

#### End-to-End Proctoring Flow
```php
it('can complete full proctoring workflow', function () {
    // 1. Create exam with proctoring
    $exam = createExamWithProctoring();
    
    // 2. Student starts exam
    $submission = startExam($exam, $student);
    
    // 3. Log proctoring events
    logProctorEvent($exam, $submission, 'webcam_capture');
    logProctorEvent($exam, $submission, 'audio_alert');
    
    // 4. Complete exam
    completeExam($submission);
    
    // 5. Review proctoring data
    $summary = getProctoringSummary($submission);
    
    // Assertions
    expect($summary['total_events'])->toBeGreaterThan(0);
    expect($summary['critical_events'])->toBe(0);
});
```

## Manual Testing Scenarios

### 1. Exam Configuration Testing

#### Test Case: Create Exam with Proctoring
**Steps:**
1. Navigate to Exam > Online Exam > Create
2. Fill in basic exam details
3. Enable proctoring in configuration section
4. Select "Medium Security" preset
5. Customize capture interval to 45 seconds
6. Add custom instructions
7. Save exam

**Expected Results:**
- Exam created successfully
- Proctoring configuration saved correctly
- Configuration preview shows enabled features
- Custom instructions displayed

#### Test Case: Preset Configuration
**Steps:**
1. Create new exam
2. Enable proctoring
3. Test each security preset (Low, Medium, High)
4. Verify configuration changes for each preset
5. Switch to custom configuration
6. Modify individual settings

**Expected Results:**
- Each preset applies correct configuration
- Custom configuration allows individual control
- Preview updates in real-time

### 2. Student Experience Testing

#### Test Case: Requirements Check
**Steps:**
1. Student accesses proctored exam
2. Requirements modal appears
3. Test webcam access grant/deny
4. Test microphone access grant/deny
5. Test screen recording access grant/deny
6. Proceed to exam

**Expected Results:**
- Modal blocks exam access until requirements met
- Clear error messages for denied permissions
- Successful validation allows exam start

#### Test Case: Proctoring During Exam
**Steps:**
1. Start proctored exam
2. Verify fullscreen enforcement
3. Test tab switching (if blocked)
4. Test copy-paste operations (if blocked)
5. Trigger audio alerts (speak loudly)
6. Move away from camera (face detection)
7. Complete exam

**Expected Results:**
- Appropriate restrictions enforced
- Violations logged correctly
- User feedback provided for violations

### 3. Review Dashboard Testing

#### Test Case: Examiner Review
**Steps:**
1. Access exam with submissions
2. Open submission detail
3. Switch to proctoring review tab
4. Review summary statistics
5. Filter events by type and severity
6. View event timeline
7. Open event details
8. View captured media

**Expected Results:**
- Summary shows correct statistics
- Filtering works correctly
- Timeline displays chronologically
- Media viewer works for images/audio
- Event details show complete information

#### Test Case: Dedicated Review Page
**Steps:**
1. Access submission list
2. Click "Proctoring Review" action
3. Review full-page dashboard
4. Export logs as CSV
5. Navigate back to submission list

**Expected Results:**
- Full-page interface loads correctly
- All dashboard features work
- CSV export contains correct data
- Navigation works properly

### 4. Performance Testing

#### Test Case: High Volume Logging
**Steps:**
1. Create exam with 1-second capture interval
2. Start exam session
3. Generate multiple event types rapidly
4. Monitor system performance
5. Check database performance
6. Verify media storage

**Expected Results:**
- System handles high-frequency logging
- No performance degradation
- All events logged correctly
- Media files stored properly

#### Test Case: Large File Uploads
**Steps:**
1. Upload maximum size media files
2. Test different file formats
3. Monitor upload progress
4. Verify file storage and retrieval

**Expected Results:**
- Large files upload successfully
- All supported formats work
- Files stored in correct locations
- Media viewer handles large files

### 5. Security Testing

#### Test Case: Authentication and Authorization
**Steps:**
1. Test API endpoints without authentication
2. Test with invalid tokens
3. Test with insufficient permissions
4. Test cross-tenant data access

**Expected Results:**
- Proper 401/403 responses
- No unauthorized data access
- Permissions enforced correctly

#### Test Case: Input Validation
**Steps:**
1. Submit invalid event types
2. Submit malformed JSON data
3. Upload invalid file types
4. Test SQL injection attempts
5. Test XSS attempts

**Expected Results:**
- All invalid input rejected
- Proper validation error messages
- No security vulnerabilities

### 6. Browser Compatibility Testing

#### Test Browsers:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

#### Test Features:
- Webcam access
- Microphone access
- Screen recording
- Fullscreen API
- File upload
- Real-time features

## Test Data Setup

### Database Seeders
```bash
# Seed test data for proctoring
php artisan db:seed --class=ProctorTestDataSeeder
```

### Factory Usage
```php
// Create exam with proctoring
$exam = OnlineExam::factory()->withProctoring()->create();

// Create proctoring logs
$logs = ProctorLog::factory()->count(10)->create([
    'online_exam_id' => $exam->id,
]);

// Create logs with media
$logWithMedia = ProctorLog::factory()->withMedia()->create();
```

## Continuous Integration

### GitHub Actions Workflow
```yaml
name: Proctoring Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: 8.1
        
    - name: Install dependencies
      run: composer install
      
    - name: Run tests
      run: php artisan test --testsuite=Unit,Feature
      
    - name: Upload coverage
      uses: codecov/codecov-action@v1
```

## Performance Benchmarks

### Expected Performance Metrics
- **API Response Time**: < 200ms for log creation
- **Database Queries**: < 5 queries per request
- **File Upload**: < 30 seconds for 10MB files
- **Dashboard Load**: < 2 seconds for 1000 events
- **CSV Export**: < 10 seconds for 10,000 records

### Load Testing
```bash
# Use Apache Bench for load testing
ab -n 1000 -c 10 -H "Authorization: Bearer token" \
   -p event.json -T application/json \
   http://localhost/api/online-exams/uuid/proctoring/logs
```

## Troubleshooting Test Issues

### Common Issues

#### Database Connection Errors
```bash
# Reset test database
php artisan migrate:fresh --env=testing
php artisan db:seed --env=testing
```

#### File Storage Issues
```bash
# Clear test storage
rm -rf storage/app/public/test-*
php artisan storage:link
```

#### Permission Issues
```bash
# Fix file permissions
chmod -R 755 storage/
chmod -R 755 bootstrap/cache/
```

### Debug Commands
```bash
# Run single test with debug output
php artisan test tests/Feature/Exam/ProctorLogTest.php --verbose

# Run tests with database queries logged
php artisan test --env=testing --verbose
```

## Test Coverage Goals

- **Unit Tests**: > 90% code coverage
- **Feature Tests**: > 80% endpoint coverage
- **Integration Tests**: All critical workflows covered
- **Manual Tests**: All user scenarios tested

## Reporting

### Generate Test Reports
```bash
# HTML coverage report
php artisan test --coverage-html reports/coverage

# JUnit XML report
php artisan test --log-junit reports/junit.xml

# Test summary
php artisan test --testdox
```

### Review Checklist

Before deploying proctoring features:
- [ ] All unit tests pass
- [ ] All feature tests pass
- [ ] Manual testing scenarios completed
- [ ] Performance benchmarks met
- [ ] Security testing passed
- [ ] Browser compatibility verified
- [ ] Documentation updated
- [ ] Test coverage goals achieved
