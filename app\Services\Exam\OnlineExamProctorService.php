<?php

namespace App\Services\Exam;

use App\Models\Exam\OnlineExam;
use App\Models\Exam\OnlineExamProctorLog;
use App\Models\Exam\OnlineExamSubmission;
use App\Models\Student\Student;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class OnlineExamProctorService
{
    /**
     * Log a proctoring event
     */
    public function logEvent(
        OnlineExamSubmission $submission,
        string $eventType,
        string $severity = 'info',
        ?string $description = null,
        ?array $data = null,
        ?UploadedFile $mediaFile = null
    ): OnlineExamProctorLog {
        $mediaPath = null;
        
        if ($mediaFile) {
            $mediaPath = $this->storeMediaFile($submission, $eventType, $mediaFile);
        }

        return OnlineExamProctorLog::create([
            'uuid' => Str::uuid(),
            'online_exam_submission_id' => $submission->id,
            'event_type' => $eventType,
            'severity' => $severity,
            'description' => $description,
            'data' => $data,
            'media_path' => $mediaPath,
            'detected_at' => now(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }

    /**
     * Store media file for proctoring event
     */
    private function storeMediaFile(OnlineExamSubmission $submission, string $eventType, UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $filename = sprintf(
            'proctor_%s_%s_%s.%s',
            $submission->uuid,
            $eventType,
            now()->format('Y-m-d_H-i-s'),
            $extension
        );

        $path = "proctoring/{$submission->exam->uuid}/{$submission->uuid}";
        
        return $file->storeAs($path, $filename, 'public');
    }

    /**
     * Log webcam capture
     */
    public function logWebcamCapture(OnlineExamSubmission $submission, UploadedFile $image, ?array $faceData = null): OnlineExamProctorLog
    {
        $data = [];
        if ($faceData) {
            $data['face_count'] = count($faceData);
            $data['faces'] = $faceData;
        }

        $severity = 'info';
        $description = 'Webcam capture';
        
        if ($faceData && count($faceData) === 0) {
            $severity = 'warning';
            $description = 'Webcam capture - No face detected';
        } elseif ($faceData && count($faceData) > 1) {
            $severity = 'critical';
            $description = 'Webcam capture - Multiple faces detected';
        }

        return $this->logEvent($submission, 'webcam_capture', $severity, $description, $data, $image);
    }

    /**
     * Log audio alert
     */
    public function logAudioAlert(OnlineExamSubmission $submission, float $audioLevel, ?UploadedFile $audioFile = null): OnlineExamProctorLog
    {
        $data = ['audio_level' => $audioLevel];
        $severity = $audioLevel > -30 ? 'warning' : 'info'; // Threshold for suspicious audio
        $description = "Audio detected - Level: {$audioLevel}dB";

        return $this->logEvent($submission, 'audio_alert', $severity, $description, $data, $audioFile);
    }

    /**
     * Log tab switch event
     */
    public function logTabSwitch(OnlineExamSubmission $submission, ?string $tabTitle = null): OnlineExamProctorLog
    {
        $data = $tabTitle ? ['tab_title' => $tabTitle] : null;
        $description = $tabTitle ? "Tab switched to: {$tabTitle}" : 'Tab switch detected';

        return $this->logEvent($submission, 'tab_switch', 'warning', $description, $data);
    }

    /**
     * Log fullscreen exit
     */
    public function logFullscreenExit(OnlineExamSubmission $submission): OnlineExamProctorLog
    {
        return $this->logEvent($submission, 'fullscreen_exit', 'critical', 'Student exited fullscreen mode');
    }

    /**
     * Log copy/paste attempt
     */
    public function logCopyPasteAttempt(OnlineExamSubmission $submission, string $action, ?string $content = null): OnlineExamProctorLog
    {
        $data = ['action' => $action];
        if ($content) {
            $data['content'] = Str::limit($content, 200);
        }

        $description = "Copy/paste attempt: {$action}";
        
        return $this->logEvent($submission, 'copy_paste_attempt', 'warning', $description, $data);
    }

    /**
     * Log face detection failure
     */
    public function logFaceDetectionFailure(OnlineExamSubmission $submission, string $reason): OnlineExamProctorLog
    {
        $data = ['failure_reason' => $reason];
        
        return $this->logEvent($submission, 'face_detection_failure', 'critical', "Face detection failed: {$reason}", $data);
    }

    /**
     * Log suspicious activity
     */
    public function logSuspiciousActivity(OnlineExamSubmission $submission, string $activity, array $details = []): OnlineExamProctorLog
    {
        return $this->logEvent($submission, 'suspicious_activity', 'critical', $activity, $details);
    }

    /**
     * Get proctoring summary for submission
     */
    public function getProctoringSummary(OnlineExamSubmission $submission): array
    {
        $logs = $submission->proctorLogs()->orderBy('detected_at')->get();
        
        $summary = [
            'total_events' => $logs->count(),
            'critical_events' => $logs->where('severity', 'critical')->count(),
            'warning_events' => $logs->where('severity', 'warning')->count(),
            'info_events' => $logs->where('severity', 'info')->count(),
            'event_types' => $logs->groupBy('event_type')->map->count(),
            'timeline' => $logs->map(function ($log) {
                return [
                    'time' => $log->detected_at->formatted,
                    'event' => $log->event_type,
                    'severity' => $log->severity,
                    'description' => $log->formatted_description,
                    'has_media' => $log->hasMedia(),
                ];
            }),
        ];

        return $summary;
    }

    /**
     * Check if submission has critical violations
     */
    public function hasCriticalViolations(OnlineExamSubmission $submission): bool
    {
        return $submission->proctorLogs()->where('severity', 'critical')->exists();
    }

    /**
     * Get violation count by type
     */
    public function getViolationCounts(OnlineExamSubmission $submission): array
    {
        return $submission->proctorLogs()
            ->selectRaw('event_type, severity, COUNT(*) as count')
            ->groupBy('event_type', 'severity')
            ->get()
            ->groupBy('event_type')
            ->map(function ($events) {
                return $events->pluck('count', 'severity')->toArray();
            })
            ->toArray();
    }

    /**
     * Validate proctoring requirements for exam start
     */
    public function validateProctorRequirements(OnlineExam $exam, Request $request): void
    {

        if (!$exam->enable_proctoring) {
            return;
        }

        $config = $exam->proctor_config;
        $errors = [];

        // Check if webcam is required and available
        if ($config['webcam_monitoring'] && !$request->boolean('webcam_available')) {
            $errors[] = 'Webcam access is required for this exam';
        }

        // Check if microphone is required and available
        if ($config['microphone_monitoring'] && !$request->boolean('microphone_available')) {
            $errors[] = 'Microphone access is required for this exam';
        }

        // Check if fullscreen is supported
        // if ($config['fullscreen_enforcement'] && !$request->boolean('fullscreen_supported')) {
        //     $errors[] = 'Fullscreen mode is required for this exam';
        // }

        if (!empty($errors)) {
            throw ValidationException::withMessages(['proctoring' => $errors]);
        }
    }

    /**
     * Initialize proctoring session
     */
    public function initializeProctoring(OnlineExamSubmission $submission): void
    {
        if (!$submission->exam->enable_proctoring) {
            return;
        }

        $this->logEvent(
            $submission,
            'proctoring_started',
            'info',
            'Proctoring session initialized',
            [
                'config' => $submission->exam->proctor_config,
                'user_agent' => request()->userAgent(),
                'screen_resolution' => request()->input('screen_resolution'),
            ]
        );
    }

    /**
     * Finalize proctoring session
     */
    public function finalizeProctoring(OnlineExamSubmission $submission): void
    {
        if (!$submission->exam->enable_proctoring) {
            return;
        }

        $summary = $this->getProctoringSummary($submission);
        
        $this->logEvent(
            $submission,
            'proctoring_ended',
            'info',
            'Proctoring session completed',
            [
                'summary' => $summary,
                'duration_seconds' => $submission->time_elapsed,
            ]
        );
    }
}
