<template>
    <ListItem
        :init-url="initUrl"
        :uuid="route.params.uuid"
        @setItems="setItems"
    >
        <DataTable
            :header="submissions.headers"
            :meta="submissions.meta"
            module="exam.online_exam.submission"
            @refresh="emitter.emit('listItems')"
        >
            <DataRow
                v-for="submission in submissions.data"
                :key="submission.uuid"
            >
                <DataCell name="studentName">
                    {{ submission.studentName }}
                    <TextMuted block>{{
                        submission.admissionNumber
                    }}</TextMuted>
                </DataCell>
                <DataCell name="courseName">
                    {{ submission.courseName + " " + submission.batchName }}
                </DataCell>
                <DataCell name="submittedAt">
                    {{ submission.submittedAt.formatted }}
                    <TextMuted block>Started At: {{
                        submission.startedAt.formatted
                    }}</TextMuted>
                </DataCell>
                <DataCell name="evaluatedAt">
                    {{ submission.evaluatedAt.formatted || "-" }}
                </DataCell>
                <DataCell name="obtainedMark">
                    {{ submission.obtainedMark }}
                </DataCell>
                <DataCell name="action">
                    <FloatingMenu>
                        <FloatingMenuItem
                            icon="fas fa-arrow-circle-right"
                            @click="view(submission)"
                            >{{
                                $trans("global.view", {
                                    attribute: $trans(
                                        "exam.online_exam.submission.submission"
                                    ),
                                })
                            }}</FloatingMenuItem
                        >
                            <FloatingMenuItem
                                v-if="onlineExam.enableProctoring"
                                icon="fas fa-shield-alt"
                                @click="viewProctorReview(submission)"
                                >{{
                                    $trans("exam.proctoring.review.view") || "Proctoring Review"
                                }}</FloatingMenuItem
                            >
                        <template v-if="!onlineExam.resultPublishedAt.value">
                            <FloatingMenuItem
                                v-if="onlineExam.canEvaluate"
                                icon="far fa-check-circle"
                                @click="evaluate(submission)"
                                >{{
                                    $trans("exam.online_exam.evaluate")
                                }}</FloatingMenuItem
                            >
                            <FloatingMenuItem
                                icon="fas fa-trash"
                                @click="
                                    emitter.emit('deleteItem', {
                                        uuid: onlineExam.uuid,
                                        moduleUuid: submission.uuid,
                                    })
                                "
                                >{{
                                    $trans("general.delete")
                                }}</FloatingMenuItem
                            >
                        </template>
                    </FloatingMenu>
                </DataCell>
            </DataRow>
            <template #actionButton> </template>
        </DataTable>
    </ListItem>

    <SubmissionDetail
        v-if="showDetail && selectedSubmission.uuid"
        :visibility="showDetail"
        :submission="selectedSubmission"
        @close="showDetail = false"
        @refresh="emitter.emit('listItems')"
    />

    <EvaluationForm
        v-if="
            showEvaluationForm &&
            onlineExam.canEvaluate &&
            selectedSubmission.uuid
        "
        :visibility="showEvaluationForm"
        :submission="selectedSubmission"
        @close="showEvaluationForm = false"
        @refresh="emitter.emit('listItems')"
    />
</template>

<script>
export default {
    name: "OnlineExamSubmissionList",
}
</script>

<script setup>
import { ref, reactive, inject, onMounted, onBeforeUnmount } from "vue"
import { useRoute, useRouter } from "vue-router"
import { perform } from "@core/helpers/action"
import EvaluationForm from "./EvaluationForm.vue"
import SubmissionDetail from "./Detail.vue"

const route = useRoute()
const router = useRouter()

const emitter = inject("emitter")

const emit = defineEmits(["refresh"])

const props = defineProps({
    onlineExam: {
        type: Object,
        default() {
            return {}
        },
    },
})

let userActions = ["filter"]

const initUrl = "exam/onlineExam/submission/"

const showEvaluationForm = ref(false)
const showDetail = ref(false)

const submissions = reactive({})
const selectedSubmission = reactive({})

const setItems = (data) => {
    Object.assign(submissions, data)
}

const completed = () => {
    Object.assign(selectedSubmission, initialSubmission)
    refreshOnlineExam()
    emitter.emit("listItems")
}

const closed = () => {
    Object.assign(selectedSubmission, initialSubmission)
    showSubmissionForm.value = false
}

const view = (submission) => {
    Object.assign(selectedSubmission, submission)
    showDetail.value = true
}

const evaluate = async (submission) => {
    Object.assign(selectedSubmission, submission)
    showEvaluationForm.value = true
}

const viewProctorReview = (submission) => {
    router.push({
        name: "ExamOnlineExamSubmissionProctorReview",
        params: {
            uuid: route.params.uuid,
            submissionUuid: submission.uuid
        }
    })
}

onMounted(async () => {
    emitter.on("actionPerformed", () => {
        refreshOnlineExam()
    })
})

onBeforeUnmount(() => {
    emitter.all.delete("actionPerformed")
})
</script>
