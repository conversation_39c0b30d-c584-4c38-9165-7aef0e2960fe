#!/bin/bash

# Install Proctoring Dependencies
echo "Installing proctoring dependencies..."

# Install TensorFlow.js for face detection
npm install @tensorflow/tfjs@^4.15.0 @tensorflow/tfjs-backend-webgl@^4.15.0

# Install face-api.js for face detection
npm install face-api.js@^0.22.2

# Install RecordRTC for screen/audio recording
npm install recordrtc@^5.6.2

echo "Proctoring dependencies installed successfully!"
echo ""
echo "Next steps:"
echo "1. Run 'npm run dev-app' to build the application"
echo "2. Download face-api.js models to public/models/ directory"
echo "3. Configure proctoring settings in exam creation"
