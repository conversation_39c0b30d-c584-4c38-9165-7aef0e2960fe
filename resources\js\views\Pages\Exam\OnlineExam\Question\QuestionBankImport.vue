<template>
    <BaseModal
        :visibility="visibility"
        @close="emit('close')"
        size="6xl"
    >
        <template #header>
            <h3 class="text-lg font-medium text-gray-900">
                {{ $trans('exam.question_bank.import_from_question_bank') }}
            </h3>
        </template>

        <div class="space-y-6">
            <!-- Search Filters -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="text-sm font-medium text-gray-700 mb-3">
                    {{ $trans('exam.question_bank.search_questions') }}
                </h4>
                <div class="grid grid-cols-3 gap-6">
                    <div class="col-span-3 sm:col-span-1">
                        <BaseSelect
                            v-model="filters.subject"
                            name="subject"
                            :label="$trans('exam.question_bank.props.subject')"
                            :options="preRequisites.subjects"
                            label-prop="name"
                            value-prop="uuid"
                        />
                    </div>
                    <div class="col-span-3 sm:col-span-1">
                        <BaseSelect
                            v-model="filters.type"
                            name="type"
                            :label="$trans('exam.question_bank.props.type')"
                            :options="preRequisites.types"
                            label-prop="label"
                            value-prop="value"
                            :disabled="isMcqExam"
                        />
                    </div>
                    <div class="col-span-3 sm:col-span-1">
                        <BaseSelectSearch
                            v-if="fetchData.isLoaded"
                            multiple
                            v-model="filters.batches"
                            name="batches"
                            :label="$trans('academic.batch.batch')"
                            value-prop="uuid"
                            :init-search="fetchData.batches"
                            search-key="course_batch"
                            search-action="academic/batch/list"
                            :close-on-select="false"
                        >
                            <template #selectedOption="slotProps">
                                {{ slotProps.value.course.name }} - {{ slotProps.value.name }}
                            </template>
                            <template #listOption="slotProps">
                                {{ slotProps.option.course.name }} - {{ slotProps.option.name }}
                            </template>
                        </BaseSelectSearch>
                    </div>
                </div>
                <div class="mt-4">
                    <BaseInput
                        type="text"
                        v-model="filters.title"
                        name="title"
                        :label="$trans('exam.question_bank.props.title')"
                        :placeholder="$trans('general.search')"
                    />
                </div>
            </div>

            <!-- Questions List -->
            <div class="max-h-96 overflow-y-auto">
                <div v-if="loading" class="text-center py-8">
                    <i class="fas fa-spinner fa-spin text-2xl text-gray-400"></i>
                </div>
                
                <div v-else-if="questions.length === 0" class="text-center py-8 text-gray-500">
                    {{ $trans('exam.question_bank.no_questions_found') }}
                </div>

                <div v-else class="space-y-3">
                    <div
                        v-for="question in questions"
                        :key="question.uuid"
                        class="border rounded-lg p-4 hover:bg-gray-50"
                        :class="{
                            'bg-blue-50 border-blue-200': selectedQuestions.includes(question.uuid)
                        }"
                    >
                        <div class="flex items-start space-x-3">
                            <BaseCheckbox
                                :model-value="selectedQuestions.includes(question.uuid)"
                                @update:model-value="toggleQuestion(question.uuid)"
                                class="mt-1"
                            />
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <h5 class="text-sm font-medium text-gray-900 truncate">
                                        {{ question.title }}
                                    </h5>
                                    <div class="flex items-center space-x-2 text-xs text-gray-500">
                                        <span class="bg-gray-100 px-2 py-1 rounded">
                                            {{ question.type?.label }}
                                        </span>
                                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                            {{ question.mark }} {{ $trans('exam.question_bank.props.mark') }}
                                        </span>
                                    </div>
                                </div>
                                <p class="text-sm text-gray-600 mt-1">
                                    {{ question.subject?.name }}
                                    <span v-if="question.batches && question.batches.length > 0" class="ml-2">
                                        • {{ question.batches.map(b => `${b.course.name} - ${b.name}`).join(', ') }}
                                    </span>
                                </p>
                                <div v-if="question.header" class="text-xs text-gray-500 mt-2 line-clamp-2">
                                    {{ question.header }}
                                </div>
                                
                                <!-- Mark Input for Selected Questions -->
                                <div v-if="selectedQuestions.includes(question.uuid)" class="mt-3">
                                    <BaseInput
                                        type="number"
                                        :model-value="questionMarks[question.uuid] || question.mark"
                                        @update:model-value="updateQuestionMark(question.uuid, $event)"
                                        :label="$trans('exam.question_bank.props.mark')"
                                        step="0.01"
                                        min="0.01"
                                        class="w-24"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Selected Questions Summary -->
            <div v-if="selectedQuestions.length > 0" class="bg-blue-50 p-4 rounded-lg">
                <h4 class="text-sm font-medium text-blue-900 mb-2">
                    {{ $trans('exam.question_bank.select_questions') }}
                </h4>
                <p class="text-sm text-blue-700">
                    {{ selectedQuestions.length }} {{ $trans('exam.online_exam.question.questions') }} {{ $trans('general.selected') }}
                </p>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-between items-center pt-6 border-t border-gray-200">
                <div class="text-sm text-gray-600">
                    <span v-if="selectedQuestions.length > 0">
                        {{ selectedQuestions.length }} {{ $trans('exam.online_exam.question.questions') }} {{ $trans('general.selected') }}
                    </span>
                </div>
                <div class="flex space-x-3">
                    <BaseButton
                        variant="soft"
                        @click="emit('close')"
                    >
                        {{ $trans('general.cancel') }}
                    </BaseButton>
                    <BaseButton
                        variant="primary"
                        @click="importQuestions"
                        :disabled="selectedQuestions.length === 0 || importing"
                        :loading="importing"
                    >
                        {{ $trans('general.import') }}
                        <span v-if="selectedQuestions.length > 0">
                            ({{ selectedQuestions.length }})
                        </span>
                    </BaseButton>
                </div>
            </div>
        </div>
    </BaseModal>
</template>

<script setup>
import { ref, reactive, onMounted, watch, inject } from "vue"
import { useStore } from "vuex"
import { debounce } from "lodash"

const store = useStore()
const $trans = inject("$trans")

const emit = defineEmits(["close", "imported"])

const props = defineProps({
    visibility: {
        type: Boolean,
        default: false,
    },
    onlineExam: {
        type: Object,
        required: true,
    },
})

const loading = ref(false)
const importing = ref(false)
const questions = ref([])
const selectedQuestions = ref([])
const questionMarks = reactive({})

const isMcqExam = ref(false)

const filters = reactive({
    subject: "",
    type: "",
    batches: [],
    title: "",
})

const preRequisites = reactive({
    subjects: [],
    types: [],
})

const fetchData = reactive({
    batches: [],
    isLoaded: false,
})

const searchQuestions = async () => {
    loading.value = true
    try {
        // Prepare filter parameters
        const params = {
            subject: filters.subject,
            type: filters.type,
            title: filters.title,
            batches: Array.isArray(filters.batches) ? filters.batches.join(',') : filters.batches,
        }

        // Remove empty parameters
        Object.keys(params).forEach(key => {
            if (!params[key] || (Array.isArray(params[key]) && params[key].length === 0)) {
                delete params[key]
            }
        })

        const response = await store.dispatch("exam/questionBank/list", {
            params: params,
        })
        questions.value = response.data || []
    } catch (error) {
        console.error("Failed to search questions:", error)
        questions.value = []
    } finally {
        loading.value = false
    }
}

const debouncedSearch = debounce(searchQuestions, 500)

// Watch all filter changes and trigger search
watch(
    () => [filters.subject, filters.type, filters.batches, filters.title],
    () => {
        debouncedSearch()
    },
    { deep: true }
)

// Debug watcher for selectedQuestions
watch(
    () => selectedQuestions.value,
    (newValue) => {
        //console.log('selectedQuestions changed:', newValue.length, newValue)
    },
    { deep: true }
)

const toggleQuestion = (questionUuid) => {
    const index = selectedQuestions.value.indexOf(questionUuid)
    if (index > -1) {
        selectedQuestions.value.splice(index, 1)
        delete questionMarks[questionUuid]
        //console.log('Question deselected:', questionUuid, 'Total selected:', selectedQuestions.value.length)
    } else {
        selectedQuestions.value.push(questionUuid)
        const question = questions.value.find(q => q.uuid === questionUuid)
        if (question) {
            questionMarks[questionUuid] = question.mark
        }
        //console.log('Question selected:', questionUuid, 'Total selected:', selectedQuestions.value.length)
    }
}

const updateQuestionMark = (questionUuid, mark) => {
    questionMarks[questionUuid] = parseFloat(mark) || 0
}

const importQuestions = async () => {
    importing.value = true
    try {
        const response = await store.dispatch("exam/questionBank/importToExam", {
            examUuid: props.onlineExam.uuid,
            form: {
                question_ids: selectedQuestions.value,
                marks: questionMarks,
            }
        })

        emit("imported", response.result)
        emit("close")
    } catch (error) {
        console.error("Failed to import questions:", error)
    } finally {
        importing.value = false
    }
}

onMounted(async () => {
    try {
        const response = await store.dispatch("exam/questionBank/preRequisiteForFilter", {})
        preRequisites.subjects = response.subjects || []
        preRequisites.types = response.types || []

        // Set fetchData for BaseSelectSearch
        fetchData.isLoaded = true

        // Initial search will be triggered by watcher
        // No need to call searchQuestions() directly
    } catch (error) {
        console.error("Failed to load prerequisites:", error)
    }
})

watch(() => props.visibility, (newValue) => {
    if (newValue) {
        selectedQuestions.value = []
        Object.keys(questionMarks).forEach(key => delete questionMarks[key])

        // If exam type is MCQ, lock the filter to MCQ
        if (props.onlineExam.type?.value === 'mcq') {
            filters.type = 'mcq'
            isMcqExam.value = true
        } else {
            filters.type = ""
            isMcqExam.value = false
        }

        // Reset other filters
        filters.subject = ""
        filters.batches = []
        filters.title = ""
        // Search will be triggered automatically by the filter watcher
    }
})
</script>
