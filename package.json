{"private": true, "type": "module", "scripts": {"dev": "vite --config vite.app.config.js && vite --config vite.site.config.js", "dev-app": "vite --config vite.app.config.js", "dev-site": "vite --config vite.site.config.js", "watch": "vite --config vite.app.config.js && vite --config vite.site.config.js", "watch-app": "vite --config vite.app.config.js", "watch-site": "vite --config vite.site.config.js", "prod": "vite build --config vite.app.config.js && vite build --config vite.site.config.js", "prod-app": "vite build --config vite.app.config.js", "prod-site": "vite build --config vite.site.config.js", "production": "vite build --config vite.app.config.js && vite build --config vite.site.config.js"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/line-clamp": "^0.4.2", "@tailwindcss/typography": "^0.5.16", "@vitejs/plugin-vue": "^5.2.1", "@vue/compiler-sfc": "^3.5.13", "acorn": "^8.14.0", "autoprefixer": "^10.4.20", "axios": "^1.7.9", "laravel-echo": "^1.18.0", "laravel-vite-plugin": "^1.1.1", "lodash": "^4.17.19", "postcss": "^8.5.1", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.10", "pusher-js": "^8.3.0", "sass": "^1.83.4", "sass-loader": "^16.0.4", "tailwindcss": "^3.4.17", "vite": "^6.0.7", "vite-plugin-compression": "^0.5.1", "vue-loader": "^17.4.2"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@highlightjs/vue-plugin": "^2.1.0", "@paystack/inline-js": "^2.22.1", "@ryangjchandler/alpine-clipboard": "^2.3.0", "@tato30/vue-pdf": "^1.11.3", "@tensorflow/tfjs": "^4.22.0", "@tensorflow/tfjs-backend-webgl": "^4.22.0", "@vueform/multiselect": "2.6.11", "@vueup/vue-quill": "^1.2.0", "aos": "^3.0.0-beta.6", "chart.js": "^4.4.6", "face-api.js": "^0.22.2", "floating-vue": "^5.2.2", "fslightbox-vue": "^2.1.3", "highlight.js": "^11.7.0", "html5-qrcode": "^2.3.8", "js-cookie": "^3.0.5", "katex": "^0.16.21", "lozad": "^1.16.0", "md-editor-v3": "^5.2.1", "mitt": "^3.0.1", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "nprogress": "^0.2.0", "quill-image-uploader": "^1.3.0", "recordrtc": "^5.6.2", "swiper": "^11.2.1", "uuid": "^11.0.5", "vue": "^3.5.13", "vue-chartjs": "^5.3.2", "vue-flatpickr-component": "^11.0.5", "vue-loading-overlay": "^6.0.6", "vue-router": "^4.5.0", "vue-sweetalert2": "^5.0.11", "vue-tel-input": "^8.3.1", "vue-toastification": "^2.0.0-rc.5", "vue3-carousel": "^0.13.0", "vue3-marquee": "^4.2.2", "vuedraggable": "^4.1.0", "vuex": "^4.1.0", "webcam-easy": "^1.1.1"}}