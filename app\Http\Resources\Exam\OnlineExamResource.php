<?php

namespace App\Http\Resources\Exam;

use App\Enums\Exam\OnlineExamQuestionType;
use App\Enums\Exam\OnlineExamType;
use App\Http\Resources\Academic\BatchSubjectRecordResource;
use App\Http\Resources\Employee\EmployeeSummaryResource;
use App\Http\Resources\MediaResource;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;

class OnlineExamResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $submissionRecord = false;
        $startedAt = null;
        $submittedAt = null;
        $hasSubmission = false;
        $canViewResults = false;
        $isStudentOrGuardian = auth()->user()->hasAnyRole(['student', 'guardian']);

        if (($request->boolean('submission') || $isStudentOrGuardian) && $isStudentOrGuardian) {
            $submissionRecord = true;
            $submission = $this->submissions->first();
            $startedAt = \Cal::dateTime($submission?->started_at);
            $submittedAt = \Cal::dateTime($submission?->submitted_at);
            $hasSubmission = (bool) $submission;

            //Determine if srudent or guardians can view results
            $canViewResults = $hasSubmission && $submittedAt && $this->result_published_at?->value;
        }

        return [
            'uuid' => $this->uuid,
            'title' => $this->title,
            'is_flexible_timing' => $this->is_flexible_timing,
            'date' => $this->date,
            'start_time' => $this->start_time,
            'end_date' => $this->end_date,
            'end_time' => $this->end_time,
            'duration_minutes' => $this->duration_minutes,
            'expiry_date' => $this->expiry_date,
            'expiry_time' => $this->expiry_time,
            'expiry_date_time' => $this->expiry_date_time?->toDateTimeString(),
            'period' => $this->is_flexible_timing ?
                ($this->duration_minutes ? $this->duration_minutes.' minutes' : 'Flexible') :
                $this->start_time->formatted.' - '.$this->end_time->formatted,
            'duration' => $this->duration,
            'flexible_duration' => $this->flexible_duration,
            'is_available' => $this->is_available,
            'type' => OnlineExamType::getDetail($this->type),
            'records' => BatchSubjectRecordResource::collection($this->whenLoaded('records')),
            'pass_percentage' => $this->pass_percentage,
            'has_negative_marking' => $this->getConfig('has_negative_marking', false),
            'negative_mark_percent_per_question' => $this->getConfig('negative_mark_percent_per_question', 0),
            'auto_publish_results_for_flexible_timing' => $this->getConfig('auto_publish_results_for_flexible_timing', true),
            'result_published_at' => $this->result_published_at,
            'published_at' => $this->published_at,
            'upcoming_threshold' => 180,
            'is_upcoming' => $this->upcoming_time > 0 ? true : false,
            'is_live' => $this->is_live,
            'is_completed' => $this->is_completed,
            $this->mergeWhen($this->upcoming_time > 0, [
                'time_left' => $this->upcoming_time,
            ]),
            $this->mergeWhen($submissionRecord, [
                'started_at' => $startedAt,
                'submitted_at' => $submittedAt,
                'has_submission' => $hasSubmission,
                'can_view_results' => $canViewResults,
                'should_show_submit_button' => $this->shouldShowSubmitButton($hasSubmission, $submittedAt),
                'should_show_result_button' => $this->shouldShowResultButton($hasSubmission, $submittedAt),
                $this->mergeWhen($this->shouldShowQuestions($request), [
                    'questions' => $this->getQuestions(),
                    'obtained_mark' => $this->getObtainedMark(),
                ]),
            ]),
            $this->mergeWhen(! auth()->user()->hasAnyRole(['student', 'guardian']), [
                'employee' => EmployeeSummaryResource::make($this->whenLoaded('employee')),
                'is_editable' => $this->is_editable,
                'is_deletable' => $this->is_deletable,
                'can_update_status' => $this->can_update_status,
                'can_manage_question' => $this->can_manage_question,
                'can_evaluate' => $this->can_evaluate,
                $this->mergeWhen($request->boolean('show_details'), [
                    'questions_count' => $this->questions_count,
                    'submissions_count' => $this->submissions_count,
                ]),
                'instructions' => $this->instructions,
                'description' => $this->description,
                'enable_proctoring' => $this->enable_proctoring,
                'proctor_config' => $this->proctor_config ?? [],
                'media_token' => $this->getMeta('media_token'),
                'media' => MediaResource::collection($this->whenLoaded('media')),
            ]),
            'created_at' => \Cal::dateTime($this->created_at),
            'updated_at' => \Cal::dateTime($this->updated_at),
        ];
    }

    private function getQuestions(): array
    {
        if (! auth()->user()->hasAnyRole(['student', 'guardian'])) {
            return [];
        }

        if (! $this->result_published_at?->value) {
            return [];
        }

        if (! $this->is_flexible_timing && !$this->is_completed) {
            return [];
        }

        $submission = $this->submissions->first();
        if (! $submission) {
            return [];
        }

        return $this->questions->map(function ($question) use ($submission) {
            $answer = collect($submission->answers)->firstWhere('uuid', $question->uuid);

            return [
                'uuid' => $question->uuid,
                'header' => $question->header,
                'type' => OnlineExamQuestionType::getDetail($question->type),
                'title' => $question->title,
                'options' => collect($question->options)->map(function ($option) {
                    return [
                        'uuid' => Arr::get($option, 'uuid'),
                        'title' => Arr::get($option, 'title'),
                        'is_correct' => (bool) Arr::get($option, 'is_correct'),
                    ];
                }),
                'max_mark' => $question->mark,
                'obtained_mark' => Arr::get($answer, 'obtained_mark'),
                'answer' => Arr::get($answer, 'answer'),
                'correct_answer' => Arr::get(collect($question->options)->firstWhere('is_correct'), 'title'),
            ];
        })->toArray();
    }

    private function shouldShowSubmitButton(bool $hasSubmission, $submittedAt): bool
    {
        // Don't show submit button if already submitted
        if ($hasSubmission && $submittedAt) {
            return false;
        }

        // Show submit button if exam is available
        if ($this->is_flexible_timing) {
            return $this->is_available;
        } else {
            return $this->is_live;
        }
    }

    private function shouldShowResultButton(bool $hasSubmission, $submittedAt): bool
    {
        // Only show result button if submitted and results are published
        return $hasSubmission &&
               $submittedAt &&
               $this->result_published_at?->value;
    }

    private function shouldShowQuestions($request): bool
    {
        // Must have show_details parameter
        if (!$request->boolean('show_details')) {
            return false;
        }

        // Must have published results
        if (!$this->result_published_at?->value) {
            return false;
        }

        // For flexible timing exams, show questions when results are published
        // regardless of completion status (since completion is based on expiry)
        if ($this->is_flexible_timing) {
            return true;
        }

        // For traditional exams, require completion
        return $this->is_completed;
    }

    private function getObtainedMark(): string
    {
        return ($this->submissions->first()?->obtained_mark ?? 0).'/'.$this->max_mark;
    }
}
