<template>
    <div class="flex items-center justify-between p-2 rounded-lg bg-gray-50 dark:bg-gray-700/50">
        <div class="flex items-center space-x-3">
            <component :is="iconComponent" class="w-4 h-4" :class="statusColor" />
            <div>
                <div class="text-sm font-medium text-gray-900 dark:text-white">
                    {{ label }}
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                    {{ count }} {{ details }}
                </div>
            </div>
        </div>
        <div class="flex items-center space-x-2">
            <div class="w-2 h-2 rounded-full" :class="statusDotColor"></div>
            <span class="text-xs font-medium" :class="statusTextColor">
                {{ statusText }}
            </span>
        </div>
    </div>
</template>

<script>
export default {
    name: "ProctorStatusItem",
}
</script>

<script setup>
import { computed } from 'vue'

const props = defineProps({
    icon: {
        type: String,
        required: true
    },
    label: {
        type: String,
        required: true
    },
    status: {
        type: String,
        default: 'inactive'
    },
    count: {
        type: Number,
        default: 0
    },
    details: {
        type: String,
        default: ''
    }
})

const iconComponent = computed(() => {
    const icons = {
        webcam: {
            template: `
                <svg fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"></path>
                </svg>
            `
        },
        microphone: {
            template: `
                <svg fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clip-rule="evenodd"></path>
                </svg>
            `
        },
        face: {
            template: `
                <svg fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"></path>
                </svg>
            `
        },
        fullscreen: {
            template: `
                <svg fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z" clip-rule="evenodd"></path>
                </svg>
            `
        },
        security: {
            template: `
                <svg fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
            `
        }
    }
    
    return icons[props.icon] || icons.webcam
})

const statusColor = computed(() => {
    switch (props.status) {
        case 'active':
            return 'text-green-500'
        case 'warning':
            return 'text-yellow-500'
        case 'error':
            return 'text-red-500'
        case 'ready':
            return 'text-blue-500'
        default:
            return 'text-gray-400'
    }
})

const statusDotColor = computed(() => {
    switch (props.status) {
        case 'active':
            return 'bg-green-500'
        case 'warning':
            return 'bg-yellow-500'
        case 'error':
            return 'bg-red-500'
        case 'ready':
            return 'bg-blue-500'
        default:
            return 'bg-gray-400'
    }
})

const statusTextColor = computed(() => {
    switch (props.status) {
        case 'active':
            return 'text-green-600 dark:text-green-400'
        case 'warning':
            return 'text-yellow-600 dark:text-yellow-400'
        case 'error':
            return 'text-red-600 dark:text-red-400'
        case 'ready':
            return 'text-blue-600 dark:text-blue-400'
        default:
            return 'text-gray-600 dark:text-gray-400'
    }
})

const statusText = computed(() => {
    switch (props.status) {
        case 'active':
            return 'Active'
        case 'warning':
            return 'Warning'
        case 'error':
            return 'Error'
        case 'ready':
            return 'Ready'
        default:
            return 'Inactive'
    }
})
</script>
