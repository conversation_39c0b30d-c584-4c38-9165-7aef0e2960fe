<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('online_exam_proctoring_logs', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->index()->unique();
            
            $table->foreignId('online_exam_submission_id')->constrained('online_exam_submissions')->onDelete('cascade');
            $table->string('event_type', 50); // webcam_capture, audio_alert, tab_switch, fullscreen_exit, face_detection, etc.
            $table->string('severity', 20)->default('info'); // info, warning, critical
            $table->text('description')->nullable();
            $table->json('data')->nullable(); // Store event-specific data
            $table->string('media_path')->nullable(); // Path to captured image/audio file
            $table->timestamp('detected_at');
            $table->ipAddress('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            
            $table->json('meta')->nullable(); // Additional metadata
            $table->timestamps();
            
            // Indexes for efficient querying
            $table->index(['online_exam_submission_id', 'event_type'], 'online_exam_submission_event_idx');
            $table->index(['online_exam_submission_id', 'severity'], 'online_exam_submission_severity_idx');
            $table->index(['detected_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('online_exam_proctoring_logs');
    }
};
