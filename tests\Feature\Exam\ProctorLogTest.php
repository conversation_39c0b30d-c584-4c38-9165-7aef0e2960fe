<?php

use App\Models\Exam\OnlineExam;
use App\Models\Exam\OnlineExamRecord;
use App\Models\Exam\ProctorLog;
use App\Models\Student\Student;
use App\Models\Team;
use App\Models\User;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

beforeEach(function () {
    $this->team = Team::factory()->create();
    $this->user = User::factory()->create(['team_id' => $this->team->id]);
    $this->student = Student::factory()->create(['team_id' => $this->team->id]);
    
    $this->exam = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => true,
        'proctor_config' => [
            'webcam_monitoring' => true,
            'microphone_monitoring' => true,
            'face_detection' => true,
            'screen_recording' => true,
        ],
    ]);
    
    $this->examRecord = OnlineExamRecord::factory()->create([
        'online_exam_id' => $this->exam->id,
        'student_id' => $this->student->id,
    ]);

    $this->actingAs($this->user);
    Storage::fake('public');
});

it('can store a proctoring log', function () {
    $data = [
        'eventType' => 'webcam_capture',
        'severity' => 'info',
        'description' => 'Webcam image captured',
        'data' => [
            'face_count' => 1,
            'confidence' => 0.95,
        ],
        'detectedAt' => now()->toISOString(),
        'ipAddress' => '***********',
        'userAgent' => 'Mozilla/5.0...',
    ];

    $response = $this->postJson(
        "/api/online-exams/{$this->exam->uuid}/proctoring/logs",
        $data
    );

    $response->assertStatus(201);
    $response->assertJsonStructure([
        'uuid',
        'event_type',
        'severity',
        'description',
        'data',
        'detected_at',
    ]);

    $this->assertDatabaseHas('proctor_logs', [
        'online_exam_id' => $this->exam->id,
        'student_id' => $this->student->id,
        'event_type' => 'webcam_capture',
        'severity' => 'info',
    ]);
});

it('can store a proctoring log with media', function () {
    $file = UploadedFile::fake()->image('webcam.jpg');

    $data = [
        'eventType' => 'webcam_capture',
        'severity' => 'info',
        'description' => 'Webcam image captured',
        'data' => ['face_count' => 1],
        'detectedAt' => now()->toISOString(),
        'media' => $file,
    ];

    $response = $this->postJson(
        "/api/online-exams/{$this->exam->uuid}/proctoring/logs",
        $data
    );

    $response->assertStatus(201);
    
    $log = ProctorLog::where('online_exam_id', $this->exam->id)->first();
    expect($log->media_path)->not->toBeNull();
    expect($log->hasMedia())->toBeTrue();
    
    Storage::disk('public')->assertExists($log->media_path);
});

it('validates required fields when storing proctoring log', function () {
    $response = $this->postJson(
        "/api/online-exams/{$this->exam->uuid}/proctoring/logs",
        []
    );

    $response->assertStatus(422);
    $response->assertJsonValidationErrors([
        'eventType',
        'severity',
        'description',
        'detectedAt',
    ]);
});

it('validates event type enum', function () {
    $data = [
        'eventType' => 'invalid_event_type',
        'severity' => 'info',
        'description' => 'Test description',
        'detectedAt' => now()->toISOString(),
    ];

    $response = $this->postJson(
        "/api/online-exams/{$this->exam->uuid}/proctoring/logs",
        $data
    );

    $response->assertStatus(422);
    $response->assertJsonValidationErrors(['eventType']);
});

it('validates severity enum', function () {
    $data = [
        'eventType' => 'webcam_capture',
        'severity' => 'invalid_severity',
        'description' => 'Test description',
        'detectedAt' => now()->toISOString(),
    ];

    $response = $this->postJson(
        "/api/online-exams/{$this->exam->uuid}/proctoring/logs",
        $data
    );

    $response->assertStatus(422);
    $response->assertJsonValidationErrors(['severity']);
});

it('can get proctoring logs for an exam', function () {
    ProctorLog::factory()->count(3)->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
    ]);

    $response = $this->getJson("/api/online-exams/{$this->exam->uuid}/proctoring/logs");

    $response->assertStatus(200);
    $response->assertJsonStructure([
        'data' => [
            '*' => [
                'uuid',
                'event_type',
                'severity',
                'description',
                'data',
                'detected_at',
                'has_media',
                'media_url',
            ],
        ],
        'meta' => [
            'current_page',
            'total',
            'per_page',
        ],
    ]);

    expect($response->json('data'))->toHaveCount(3);
});

it('can filter proctoring logs by event type', function () {
    ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'event_type' => 'webcam_capture',
    ]);

    ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'event_type' => 'audio_alert',
    ]);

    $response = $this->getJson("/api/online-exams/{$this->exam->uuid}/proctoring/logs?event_type=webcam_capture");

    $response->assertStatus(200);
    expect($response->json('data'))->toHaveCount(1);
    expect($response->json('data.0.event_type'))->toBe('webcam_capture');
});

it('can filter proctoring logs by severity', function () {
    ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'severity' => 'critical',
    ]);

    ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'severity' => 'info',
    ]);

    $response = $this->getJson("/api/online-exams/{$this->exam->uuid}/proctoring/logs?severity=critical");

    $response->assertStatus(200);
    expect($response->json('data'))->toHaveCount(1);
    expect($response->json('data.0.severity'))->toBe('critical');
});

it('can get proctoring summary for an exam submission', function () {
    ProctorLog::factory()->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'severity' => 'critical',
        'event_type' => 'face_detection_failure',
    ]);

    ProctorLog::factory()->count(2)->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'severity' => 'warning',
        'event_type' => 'tab_switch',
    ]);

    ProctorLog::factory()->count(3)->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
        'severity' => 'info',
        'event_type' => 'webcam_capture',
    ]);

    $response = $this->getJson("/api/online-exams/{$this->exam->uuid}/proctoring/submissions/{$this->examRecord->uuid}/summary");

    $response->assertStatus(200);
    $response->assertJsonStructure([
        'summary' => [
            'total_events',
            'critical_events',
            'warning_events',
            'info_events',
            'event_types',
            'timeline',
        ],
    ]);

    $summary = $response->json('summary');
    expect($summary['total_events'])->toBe(6);
    expect($summary['critical_events'])->toBe(1);
    expect($summary['warning_events'])->toBe(2);
    expect($summary['info_events'])->toBe(3);
});

it('requires authentication to access proctoring endpoints', function () {
    $this->withoutMiddleware();
    
    $response = $this->getJson("/api/online-exams/{$this->exam->uuid}/proctoring/logs");
    $response->assertStatus(401);

    $response = $this->postJson("/api/online-exams/{$this->exam->uuid}/proctoring/logs", []);
    $response->assertStatus(401);
});

it('requires proper permissions to access proctoring endpoints', function () {
    $unauthorizedUser = User::factory()->create(['team_id' => $this->team->id]);
    $this->actingAs($unauthorizedUser);

    $response = $this->getJson("/api/online-exams/{$this->exam->uuid}/proctoring/logs");
    $response->assertStatus(403);
});

it('returns 404 for non-existent exam', function () {
    $response = $this->getJson("/api/online-exams/non-existent-uuid/proctoring/logs");
    $response->assertStatus(404);
});

it('returns 403 when proctoring is disabled for exam', function () {
    $examWithoutProctoring = OnlineExam::factory()->create([
        'team_id' => $this->team->id,
        'enable_proctoring' => false,
    ]);

    $response = $this->postJson(
        "/api/online-exams/{$examWithoutProctoring->uuid}/proctoring/logs",
        [
            'eventType' => 'webcam_capture',
            'severity' => 'info',
            'description' => 'Test',
            'detectedAt' => now()->toISOString(),
        ]
    );

    $response->assertStatus(403);
    $response->assertJson([
        'message' => 'Proctoring is not enabled for this exam',
    ]);
});

it('can export proctoring logs as CSV', function () {
    ProctorLog::factory()->count(5)->create([
        'online_exam_id' => $this->exam->id,
        'online_exam_record_id' => $this->examRecord->id,
        'student_id' => $this->student->id,
    ]);

    $response = $this->getJson("/api/online-exams/{$this->exam->uuid}/proctoring/logs?export=true");

    $response->assertStatus(200);
    $response->assertHeader('Content-Type', 'text/csv; charset=UTF-8');
    $response->assertHeader('Content-Disposition', 'attachment; filename="proctoring-logs-' . $this->exam->uuid . '.csv"');
    
    $csvContent = $response->getContent();
    expect($csvContent)->toContain('Event Type,Severity,Description,Detected At');
});
