<template>
    <div class="space-y-6">
        <!-- Proctoring Summary Header -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    {{ $trans("exam.proctoring.review.title") || "Proctoring Review" }}
                </h3>
                <div class="flex items-center space-x-2">
                    <span 
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        :class="overallStatusClass"
                    >
                        {{ overallStatusText }}
                    </span>
                    <BaseButton
                        size="sm"
                        design="secondary"
                        @click="refreshData"
                        :disabled="isLoading"
                    >
                        <i class="fas fa-sync-alt mr-1" :class="{ 'animate-spin': isLoading }"></i>
                        {{ $trans("general.refresh") || "Refresh" }}
                    </BaseButton>
                </div>
            </div>

            <!-- Summary Stats -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {{ summary.total_events || 0 }}
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        {{ $trans("exam.proctoring.review.total_events") || "Total Events" }}
                    </div>
                </div>
                <div class="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="text-2xl font-bold text-red-600 dark:text-red-400">
                        {{ summary.critical_events || 0 }}
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        {{ $trans("exam.proctoring.review.critical_events") || "Critical" }}
                    </div>
                </div>
                <div class="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                        {{ summary.warning_events || 0 }}
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        {{ $trans("exam.proctoring.review.warning_events") || "Warnings" }}
                    </div>
                </div>
                <div class="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                        {{ summary.info_events || 0 }}
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        {{ $trans("exam.proctoring.review.info_events") || "Info" }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Controls -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-4">
            <div class="flex flex-wrap items-center gap-4">
                <div class="flex-1 min-w-0">
                    <BaseSelect
                        v-model="filters.event_type"
                        :options="eventTypeOptions"
                        :placeholder="$trans('exam.proctoring.review.filter_by_type') || 'Filter by Event Type'"
                        @change="applyFilters"
                    />
                </div>
                <div class="flex-1 min-w-0">
                    <BaseSelect
                        v-model="filters.severity"
                        :options="severityOptions"
                        :placeholder="$trans('exam.proctoring.review.filter_by_severity') || 'Filter by Severity'"
                        @change="applyFilters"
                    />
                </div>
                <div class="flex items-center space-x-2">
                    <BaseButton
                        size="sm"
                        design="secondary"
                        @click="clearFilters"
                    >
                        {{ $trans("general.clear") || "Clear" }}
                    </BaseButton>
                    <BaseButton
                        size="sm"
                        design="primary"
                        @click="exportLogs"
                    >
                        <i class="fas fa-download mr-1"></i>
                        {{ $trans("general.export") || "Export" }}
                    </BaseButton>
                </div>
            </div>
        </div>

        <!-- Event Timeline -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
            <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                    {{ $trans("exam.proctoring.review.timeline") || "Event Timeline" }}
                </h4>
            </div>
            
            <div class="p-4">
                <ProctorEventTimeline
                    :events="filteredLogs"
                    :is-loading="isLoading"
                    @view-media="viewMedia"
                    @view-details="viewEventDetails"
                />
            </div>
        </div>

        <!-- Event Details Modal -->
        <ProctorEventDetailModal
            v-if="selectedEvent"
            :event="selectedEvent"
            :visibility="showEventDetail"
            @close="closeEventDetail"
        />

        <!-- Media Viewer Modal -->
        <ProctorMediaViewer
            v-if="selectedMedia"
            :media="selectedMedia"
            :visibility="showMediaViewer"
            @close="closeMediaViewer"
        />
    </div>
</template>

<script>
export default {
    name: "ProctorReviewDashboard",
}
</script>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import * as Api from '@core/apis'
import ProctorEventTimeline from './ProctorEventTimeline.vue'
import ProctorEventDetailModal from './ProctorEventDetailModal.vue'
import ProctorMediaViewer from './ProctorMediaViewer.vue'

const props = defineProps({
    examUuid: {
        type: String,
        required: true
    },
    submissionUuid: {
        type: String,
        required: true
    }
})

// State
const isLoading = ref(false)
const summary = reactive({})
const logs = ref([])
const selectedEvent = ref(null)
const selectedMedia = ref(null)
const showEventDetail = ref(false)
const showMediaViewer = ref(false)

// Filters
const filters = reactive({
    event_type: '',
    severity: ''
})

// Filter options
const eventTypeOptions = [
    { label: 'All Events', value: '' },
    { label: 'Webcam Capture', value: 'webcam_capture' },
    { label: 'Audio Alert', value: 'audio_alert' },
    { label: 'Tab Switch', value: 'tab_switch' },
    { label: 'Fullscreen Exit', value: 'fullscreen_exit' },
    { label: 'Copy/Paste Attempt', value: 'copy_paste_attempt' },
    { label: 'Face Detection Failure', value: 'face_detection_failure' },
    { label: 'Suspicious Activity', value: 'suspicious_activity' }
]

const severityOptions = [
    { label: 'All Severities', value: '' },
    { label: 'Critical', value: 'critical' },
    { label: 'Warning', value: 'warning' },
    { label: 'Info', value: 'info' }
]

// Computed
const filteredLogs = computed(() => {
    let filtered = logs.value

    if (filters.event_type) {
        filtered = filtered.filter(log => log.event_type === filters.event_type)
    }

    if (filters.severity) {
        filtered = filtered.filter(log => log.severity === filters.severity)
    }

    return filtered
})

const overallStatusClass = computed(() => {
    const criticalCount = summary.critical_events || 0
    const warningCount = summary.warning_events || 0

    if (criticalCount > 0) {
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
    } else if (warningCount > 0) {
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
    } else {
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
    }
})

const overallStatusText = computed(() => {
    const criticalCount = summary.critical_events || 0
    const warningCount = summary.warning_events || 0

    if (criticalCount > 0) {
        return 'Requires Review'
    } else if (warningCount > 0) {
        return 'Minor Issues'
    } else {
        return 'Clean Session'
    }
})

// Methods
const loadProctoringSummary = async () => {
    try {
        isLoading.value = true
        const response = await Api.custom({
            url: `app/online-exams/${props.examUuid}/proctoring/submissions/${props.submissionUuid}/summary`,
            method: 'GET'
        })

        Object.assign(summary, response.summary)
    } catch (error) {
        console.error('Failed to load proctoring summary:', error)
    } finally {
        isLoading.value = false
    }
}

const loadProctorLogs = async () => {
    try {
        isLoading.value = true
        const response = await Api.custom({
            url: `app/online-exams/${props.examUuid}/proctoring/submissions/${props.submissionUuid}/logs?per_page=100&event_type=${filters.event_type}&severity=${filters.severity}`,
            method: 'GET'
        })

        logs.value = response.data || []
    } catch (error) {
        console.error('Failed to load proctor logs:', error)
    } finally {
        isLoading.value = false
    }
}

const refreshData = async () => {
    await Promise.all([
        loadProctoringSummary(),
        loadProctorLogs()
    ])
}

const applyFilters = () => {
    loadProctorLogs()
}

const clearFilters = () => {
    filters.event_type = ''
    filters.severity = ''
    loadProctorLogs()
}

const viewEventDetails = (event) => {
    selectedEvent.value = event
    showEventDetail.value = true
}

const closeEventDetail = () => {
    selectedEvent.value = null
    showEventDetail.value = false
}

const viewMedia = (media) => {
    selectedMedia.value = media
    showMediaViewer.value = true
}

const closeMediaViewer = () => {
    selectedMedia.value = null
    showMediaViewer.value = false
}

const exportLogs = async () => {
    try {
        const response = await Api.custom({
            url: `app/online-exams/${props.examUuid}/proctoring/submissions/${props.submissionUuid}/logs?export=true&event_type=${filters.event_type}&severity=${filters.severity}`,
            method: 'GET'
        })

        // Create and download CSV file
        const blob = new Blob([response], { type: 'text/csv' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `proctoring-logs-${props.submissionUuid}.csv`
        link.click()
        window.URL.revokeObjectURL(url)
    } catch (error) {
        console.error('Failed to export logs:', error)
    }
}

// Lifecycle
onMounted(() => {
    refreshData()
})

// Watch for prop changes
watch(() => props.submissionUuid, () => {
    if (props.submissionUuid) {
        refreshData()
    }
}, { immediate: true })
</script>
