<template>
    <div class="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
        <div class="flex items-start space-x-3">
            <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <div class="flex-1">
                <h3 class="text-lg font-semibold text-yellow-800 dark:text-yellow-200">
                    {{ $trans("exam.proctoring.setup_required") || "Proctoring Setup Required" }}
                </h3>
                <p class="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
                    {{ $trans("exam.proctoring.setup_description") || "This exam requires proctoring. Please ensure your camera and microphone are working properly." }}
                </p>
                
                <!-- Proctoring Requirements -->
                <div class="mt-4 space-y-2">
                    <ProctorRequirement
                        v-if="config.webcamMonitoring"
                        icon="webcam"
                        :text="$trans('exam.proctoring.webcam_required') || 'Webcam monitoring enabled'"
                    />
                    
                    <ProctorRequirement
                        v-if="config.microphoneMonitoring"
                        icon="microphone"
                        :text="$trans('exam.proctoring.microphone_required') || 'Microphone monitoring enabled'"
                    />
                    
                    <ProctorRequirement
                        v-if="config.fullscreenEnforcement"
                        icon="fullscreen"
                        :text="$trans('exam.proctoring.fullscreen_required') || 'Fullscreen mode required'"
                    />
                    
                    <ProctorRequirement
                        v-if="config.copyPasteBlocking"
                        icon="clipboard"
                        :text="$trans('exam.proctoring.copy_paste_blocked') || 'Copy/paste operations blocked'"
                    />
                    
                    <ProctorRequirement
                        v-if="config.faceDetection"
                        icon="face"
                        :text="$trans('exam.proctoring.face_detection_enabled') || 'Face detection enabled'"
                    />
                    
                    <ProctorRequirement
                        v-if="config.screenRecording"
                        icon="screen"
                        :text="$trans('exam.proctoring.screen_recording_enabled') || 'Screen recording enabled'"
                    />
                </div>
                
                <!-- Proctoring Status -->
                <div v-if="isInitialized" class="mt-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded">
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-sm font-semibold text-green-800 dark:text-green-200">
                            {{ $trans("exam.proctoring.setup_complete") || "Proctoring setup complete" }}
                        </span>
                    </div>
                </div>
                
                <!-- Proctoring Errors -->
                <div v-if="errors.length > 0" class="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded">
                    <h4 class="text-sm font-semibold text-red-800 dark:text-red-200 mb-2">
                        {{ $trans("exam.proctoring.setup_errors") || "Setup Issues:" }}
                    </h4>
                    <ul class="text-sm text-red-700 dark:text-red-300 space-y-1">
                        <li v-for="error in errors" :key="error" class="flex items-start space-x-1">
                            <span class="text-red-500 mt-0.5">•</span>
                            <span>{{ error }}</span>
                        </li>
                    </ul>
                </div>
                
                <!-- Test Proctoring Button -->
                <div v-if="!isInitialized" class="mt-4">
                    <BaseButton
                        design="warning"
                        size="sm"
                        @click="$emit('test-proctoring')"
                        :disabled="isInitializing"
                    >
                        {{ isInitializing 
                            ? ($trans("exam.proctoring.initializing") || "Initializing...")
                            : ($trans("exam.proctoring.test_setup") || "Test Proctoring Setup")
                        }}
                    </BaseButton>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "ProctorSetupCard",
}
</script>

<script setup>
import ProctorRequirement from './ProctorRequirement.vue'

defineEmits(['test-proctoring'])

defineProps({
    config: {
        type: Object,
        default: () => ({})
    },
    isInitialized: {
        type: Boolean,
        default: false
    },
    isInitializing: {
        type: Boolean,
        default: false
    },
    errors: {
        type: Array,
        default: () => []
    }
})
</script>
