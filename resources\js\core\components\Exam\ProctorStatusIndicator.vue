<template>
    <div v-if="isActive" class="flex items-center space-x-2">
        <!-- Webcam Status -->
        <div 
            v-if="config.webcam_monitoring" 
            class="flex items-center space-x-1"
            :title="$trans('exam.proctoring.webcam_status')"
        >
            <svg 
                class="w-4 h-4" 
                :class="getStatusColor('webcam')" 
                fill="currentColor" 
                viewBox="0 0 20 20"
            >
                <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"></path>
            </svg>
            <span class="text-xs text-gray-600 dark:text-gray-400">
                {{ eventCounts.webcamCaptures }}
            </span>
        </div>
        
        <!-- Microphone Status -->
        <div 
            v-if="config.microphone_monitoring" 
            class="flex items-center space-x-1"
            :title="$trans('exam.proctoring.microphone_status')"
        >
            <svg 
                class="w-4 h-4" 
                :class="getStatusColor('microphone')" 
                fill="currentColor" 
                viewBox="0 0 20 20"
            >
                <path fill-rule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-xs text-gray-600 dark:text-gray-400">
                {{ eventCounts.audioAlerts }}
            </span>
        </div>
        
        <!-- Face Detection Status -->
        <div 
            v-if="config.face_detection" 
            class="flex items-center space-x-1"
            :title="$trans('exam.proctoring.face_detection_status')"
        >
            <svg 
                class="w-4 h-4" 
                :class="getStatusColor('faceDetection')" 
                fill="currentColor" 
                viewBox="0 0 20 20"
            >
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-xs text-gray-600 dark:text-gray-400">
                {{ eventCounts.faceDetectionFailures }}
            </span>
        </div>
        
        <!-- Fullscreen Status -->
        <div 
            v-if="config.fullscreen_enforcement" 
            class="flex items-center space-x-1"
            :title="$trans('exam.proctoring.fullscreen_status')"
        >
            <svg 
                class="w-4 h-4" 
                :class="getStatusColor('fullscreen')" 
                fill="currentColor" 
                viewBox="0 0 20 20"
            >
                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-xs text-gray-600 dark:text-gray-400">
                {{ eventCounts.fullscreenExits }}
            </span>
        </div>
        
        <!-- Violations Indicator -->
        <div v-if="totalViolations > 0" class="flex items-center space-x-1">
            <svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-xs text-red-600 dark:text-red-400 font-semibold">
                {{ totalViolations }}
            </span>
        </div>
    </div>
</template>

<script>
export default {
    name: "ProctorStatusIndicator",
}
</script>

<script setup>
import { computed } from 'vue'

const props = defineProps({
    isActive: {
        type: Boolean,
        default: false
    },
    config: {
        type: Object,
        default: () => ({})
    },
    status: {
        type: Object,
        default: () => ({
            webcamStatus: 'inactive',
            microphoneStatus: 'inactive',
            faceDetectionStatus: 'inactive',
            fullscreenStatus: 'inactive'
        })
    },
    eventCounts: {
        type: Object,
        default: () => ({
            webcamCaptures: 0,
            audioAlerts: 0,
            tabSwitches: 0,
            fullscreenExits: 0,
            copyPasteAttempts: 0,
            faceDetectionFailures: 0
        })
    }
})

const getStatusColor = (type) => {
    const statusMap = {
        webcam: props.status.webcamStatus,
        microphone: props.status.microphoneStatus,
        faceDetection: props.status.faceDetectionStatus,
        fullscreen: props.status.fullscreenStatus
    }
    
    const status = statusMap[type]
    
    switch (status) {
        case 'active':
            return 'text-green-500'
        case 'ready':
            return 'text-yellow-500'
        case 'error':
            return 'text-red-500'
        default:
            return 'text-gray-400'
    }
}

const totalViolations = computed(() => {
    return props.eventCounts.tabSwitches + 
           props.eventCounts.fullscreenExits + 
           props.eventCounts.copyPasteAttempts + 
           props.eventCounts.faceDetectionFailures
})
</script>
