<?php

namespace App\Services\Exam;

use App\Enums\Exam\OnlineExamType;
use App\Helpers\CalHelper;
use App\Http\Resources\Academic\SubjectResource;
use App\Models\Academic\BatchSubjectRecord;
use App\Models\Academic\Subject;
use App\Models\Employee\Employee;
use App\Models\Exam\OnlineExam;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class OnlineExamService
{
    public function preRequisite(Request $request)
    {
        $subjects = SubjectResource::collection(Subject::query()
            ->byPeriod()
            ->get());

        $types = OnlineExamType::getOptions();

        return compact('types', 'subjects');
    }

    public function create(Request $request): OnlineExam
    {
        \DB::beginTransaction();

        $onlineExam = OnlineExam::forceCreate($this->formatParams($request));

        $this->updateBatchSubjectRecords($request, $onlineExam);

        $onlineExam->addMedia($request);

        \DB::commit();

        return $onlineExam;
    }

    private function formatParams(Request $request, ?OnlineExam $onlineExam = null): array
    {
        $formatted = [
            'title' => $request->title,
            'type' => $request->type,
            'is_flexible_timing' => $request->boolean('is_flexible_timing', false),
            'pass_percentage' => $request->float('pass_percentage', 0),
            'instructions' => clean($request->instructions),
            'description' => clean($request->description),
            'enable_proctoring' => $request->boolean('enable_proctoring', false),
            'proctor_config' => $request->boolean('enable_proctoring', false) ? $request->input('proctor_config', []) : [],
        ];

        // if ($request->boolean('is_flexible_timing', false)) {
        //     // Flexible timing configuration
        //     $formatted['duration_minutes'] = $request->integer('duration_minutes');
        //     $formatted['expiry_date'] = $request->expiry_date;
            
        //     // Handle expiry_time with proper timezone conversion
        //     if ($request->expiry_date && $request->expiry_time) {
        //         $formatted['expiry_time'] = CalHelper::storeDateTime($request->expiry_date.' '.$request->expiry_time)?->toTimeString();
        //     } elseif ($request->expiry_date && !$request->expiry_time) {
        //         // Default to end of day in user's timezone, then convert to UTC
        //         $formatted['expiry_time'] = CalHelper::storeDateTime($request->expiry_date.' 23:59:59')?->toTimeString();
        //     } else {
        //         $formatted['expiry_time'] = null;
        //     }


        //     // Set default values for traditional fields to maintain compatibility
        //     $formatted['date'] = now()->toDateString();
        //     $formatted['start_time'] = '00:00:00';
        //     $formatted['end_date'] = now()->toDateString();
        //     $formatted['end_time'] = '23:59:59';
        // }
        
        
        if ($request->boolean('is_flexible_timing', false)) {
            // Flexible timing configuration
            $formatted['duration_minutes'] = $request->integer('duration_minutes');
            $formatted['expiry_date'] = $request->expiry_date;

            // Handle expiry_time with proper timezone conversion
            if ($formatted['expiry_date'] && $request->expiry_time) {
                $formatted['expiry_time'] = CalHelper::storeDateTime($formatted['expiry_date'].' '.$request->expiry_time)?->toTimeString();
            } elseif ($formatted['expiry_date'] && !$request->expiry_time) {
                // Default to end of day in user's timezone, then convert to UTC
                $formatted['expiry_time'] = CalHelper::storeDateTime($formatted['expiry_date'].' 23:59:59')?->toTimeString();
            } else {
                $formatted['expiry_time'] = null;
            }

            // Set default values for traditional fields to maintain compatibility
            // Only set defaults during creation, preserve existing values during updates
            if (!$onlineExam) {
                // New exam - set default start date and end date
                $date = $request->date ?? now()->addYear()->toDateString();
                $startTime = $request->start_time ?? '00:00:00';
                $endDate = $request->expiry_date ?? now()->addYear()->toDateString();
                $endTime = $request->expiry_time ?? '23:59:59';

                $startTime1 = CalHelper::storeDateTime($date.' '.$startTime)?->toTimeString();
                $endTime1 = CalHelper::storeDateTime($endDate.' '.$endTime)?->toTimeString();

                $formatted['date'] = $date;
                $formatted['start_time'] = $startTime1;
                $formatted['end_date'] = $endDate;
                $formatted['end_time'] = $endTime1;
            } else {
                // Update exam - preserve existing start date, only update end_date if expiry changed
                if ($request->has('expiry_date') && $request->expiry_date) {
                    $formatted['end_date'] = $request->expiry_date;
                } elseif ($request->has('expiry_date') && !$request->expiry_date) {
                    // Expiry date was cleared, set end_date to next year
                    $formatted['end_date'] = now()->addYear()->toDateString();
                }
                // Don't update date, start_time, end_time unless specifically needed
            }
        } else {
            // Traditional timing configuration
            $startTime = CalHelper::storeDateTime($request->date.' '.$request->start_time)?->toTimeString();
            $endTime = CalHelper::storeDateTime($request->date.' '.$request->end_time)?->toTimeString();

            $formatted['date'] = $request->date;
            $formatted['start_time'] = $startTime;
            $formatted['end_date'] = $request->end_date ?: $request->date;
            $formatted['end_time'] = $endTime;
            $formatted['duration_minutes'] = null;
            $formatted['expiry_date'] = null;
            $formatted['expiry_time'] = null;
        }

        if (! $onlineExam) {
            $formatted['period_id'] = auth()->user()->current_period_id;
            $formatted['employee_id'] = Employee::auth()->first()?->id;
        }

        $config = $onlineExam?->config ?? [];

        $config['has_negative_marking'] = $request->boolean('has_negative_marking');
        $config['negative_mark_percent_per_question'] = $request->float('negative_mark_percent_per_question', 0);

        // Auto-publish results configuration for flexible timing exams
        if ($request->boolean('is_flexible_timing', false)) {
            $config['auto_publish_results_for_flexible_timing'] = $request->boolean('auto_publish_results_for_flexible_timing', true);
        } else {
            // Remove the config for traditional exams
            unset($config['auto_publish_results_for_flexible_timing']);
        }

        $formatted['config'] = $config;

        return $formatted;
    }

    private function updateBatchSubjectRecords(Request $request, OnlineExam $onlineExam)
    {
        $usedIds = [];
        foreach ($request->batch_ids as $batchId) {
            $usedIds[] = [
                'batch_id' => $batchId,
                'subject_id' => $request->subject_id,
            ];
            BatchSubjectRecord::firstOrCreate([
                'model_type' => $onlineExam->getMorphClass(),
                'model_id' => $onlineExam->id,
                'batch_id' => $batchId,
                'subject_id' => $request->subject_id,
            ]);
        }
        $records = BatchSubjectRecord::query()
            ->whereModelType($onlineExam->getMorphClass())
            ->whereModelId($onlineExam->id)
            ->get();
        $usedIds = collect($usedIds);
        foreach ($records as $record) {
            if (! $usedIds->where('batch_id', $record->batch_id)->where('subject_id', $record->subject_id)->count()) {
                $record->delete();
            }
        }
    }

    public function update(Request $request, OnlineExam $onlineExam): void
    {
        if (! $onlineExam->is_editable) {
            throw ValidationException::withMessages(['message' => trans('user.errors.permission_denied')]);
        }

        \DB::beginTransaction();

        $onlineExam->forceFill($this->formatParams($request, $onlineExam))->save();

        $this->updateBatchSubjectRecords($request, $onlineExam);

        $onlineExam->updateMedia($request);

        \DB::commit();
    }

    public function deletable(OnlineExam $onlineExam): bool
    {
        if (! $onlineExam->is_deletable) {
            throw ValidationException::withMessages(['message' => trans('user.errors.permission_denied')]);
        }

        // $onlineExamScheduleExists = \DB::table('exam_schedules')
        //     ->whereExamId($onlineExam->id)
        //     ->exists();

        // if ($onlineExamScheduleExists) {
        //     throw ValidationException::withMessages(['message' => trans('global.associated_with_dependency', ['attribute' => trans('exam.exam'), 'dependency' => trans('exam.schedule.schedule')])]);
        // }

        return true;
    }
}
